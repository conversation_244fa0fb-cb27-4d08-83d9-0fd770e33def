<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文字转语音测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        textarea {
            height: 100px;
            resize: vertical;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        

        
        .config {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .char-count {
            text-align: right;
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .input-group {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            position: relative;
        }

        .input-group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .input-group-title {
            font-weight: bold;
            color: #495057;
            font-size: 14px;
        }

        .input-group-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .input-group textarea {
            margin-bottom: 5px;
        }

        .send-btn {
            background-color: #28a745;
            font-size: 14px;
            padding: 8px 16px;
        }

        .send-btn:hover {
            background-color: #218838;
        }

        .delete-btn {
            background-color: #dc3545;
            font-size: 12px;
            padding: 6px 12px;
        }

        .delete-btn:hover {
            background-color: #c82333;
        }

        .add-input-btn {
            background-color: #17a2b8;
            margin-bottom: 20px;
        }

        .add-input-btn:hover {
            background-color: #138496;
        }

        .main-input-group {
            border: 2px solid #007bff;
            background-color: #e7f3ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 文字转语音测试页面</h1>
        
        <div class="config">
            <div class="form-group">
                <label for="serverUrl">服务器地址:</label>
                <input type="text" id="serverUrl" value="http://localhost:8080" placeholder="http://localhost:8080">
            </div>
        </div>
        
        <!-- 主输入框 -->
        <div class="input-group main-input-group">
            <div class="input-group-header">
                <div class="input-group-title">📝 主输入框</div>
                <div class="input-group-controls">
                    <button class="send-btn" onclick="sendTTSFromGroup('main')">🎵 发送</button>
                </div>
            </div>
            <textarea id="textInput" placeholder="请输入要转换为语音的文字..."></textarea>
            <div class="char-count">
                字符数: <span id="charCount">0</span> / 1000字节
            </div>
        </div>

        <!-- 动态输入框容器 -->
        <button class="add-input-btn" onclick="addInputGroup()">➕ 添加新的输入框</button>
        <div id="dynamicInputs"></div>
        

        <div class="config" style="background-color: #e8f4fd; border: 1px solid #bee5eb;">
            <h3 style="margin-top: 0; color: #0c5460;">💡 使用说明</h3>
            <p><strong>自动对话模式：</strong>唤醒词 → 录音 → 语音识别 → 大模型回复</p>
            <p><strong>手动回复模式：</strong>唤醒词 → 等待手动输入 → 直接语音合成</p>
            <p>手动模式下，唤醒后不会录音上传，只等待您通过网页输入回复内容。</p>
        </div>
        
        <div style="border-top: 1px solid #dee2e6; padding-top: 20px; margin-top: 20px;">
            <button onclick="sendTTS()" id="sendBtn">🎵 发送主输入框</button>
            <button onclick="testConnection()" id="testBtn">🔗 测试连接</button>
            <button onclick="toggleMode()" id="modeBtn">🔄 切换模式</button>
            <button onclick="checkPending()" id="pendingBtn">📝 检查待回复</button>
            <button onclick="clearAllText()">🗑️ 清空所有文字</button>
        </div>

        <div id="modeStatus" class="status info" style="display: none;">
            当前模式: <span id="currentMode">未知</span>
        </div>

        <div id="pendingStatus" class="status info" style="display: none;">
            待回复内容: <span id="pendingText">无</span>
        </div>
        
        <div id="status"></div>
    </div>

    <script>
        let inputGroupCounter = 0;

        // 更新字符计数
        function updateCharCount(inputId = 'textInput', countId = 'charCount') {
            const text = document.getElementById(inputId).value;
            const byteLength = new TextEncoder().encode(text).length;
            document.getElementById(countId).textContent = byteLength;

            // 如果超过1000字节，显示警告
            if (byteLength > 1000) {
                document.getElementById(countId).style.color = 'red';
            } else {
                document.getElementById(countId).style.color = '#666';
            }
        }

        // 添加新的输入框组
        function addInputGroup() {
            inputGroupCounter++;
            const groupId = `group_${inputGroupCounter}`;
            const textareaId = `textarea_${inputGroupCounter}`;
            const countId = `count_${inputGroupCounter}`;

            const groupHtml = `
                <div class="input-group" id="${groupId}">
                    <div class="input-group-header">
                        <div class="input-group-title">📝 输入框 ${inputGroupCounter}</div>
                        <div class="input-group-controls">
                            <button class="send-btn" onclick="sendTTSFromGroup('${groupId}')">🎵 发送</button>
                            <button class="delete-btn" onclick="removeInputGroup('${groupId}')">🗑️ 删除</button>
                        </div>
                    </div>
                    <textarea id="${textareaId}" placeholder="请输入要转换为语音的文字..."></textarea>
                    <div class="char-count">
                        字符数: <span id="${countId}">0</span> / 1000字节
                    </div>
                </div>
            `;

            document.getElementById('dynamicInputs').insertAdjacentHTML('beforeend', groupHtml);

            // 为新的textarea添加事件监听器
            const textarea = document.getElementById(textareaId);
            textarea.addEventListener('input', () => updateCharCount(textareaId, countId));
            textarea.addEventListener('keydown', function(event) {
                if (event.ctrlKey && event.key === 'Enter') {
                    sendTTSFromGroup(groupId);
                }
            });

            // 初始化字符计数
            updateCharCount(textareaId, countId);
        }

        // 删除输入框组
        function removeInputGroup(groupId) {
            const group = document.getElementById(groupId);
            if (group) {
                group.remove();
            }
        }
        
        // 从指定输入框组发送TTS
        function sendTTSFromGroup(groupId) {
            let textareaId, text;

            if (groupId === 'main') {
                textareaId = 'textInput';
                text = document.getElementById('textInput').value.trim();
            } else {
                // 查找该组中的textarea
                const group = document.getElementById(groupId);
                const textarea = group.querySelector('textarea');
                textareaId = textarea.id;
                text = textarea.value.trim();
            }

            if (!text) {
                showStatus('❌ 请输入要合成的文字', 'error');
                return;
            }

            const byteLength = new TextEncoder().encode(text).length;
            if (byteLength > 1000) {
                showStatus('❌ 文字太长，超过1000字节限制', 'error');
                return;
            }

            // 调用原有的TTS发送逻辑，但使用指定的文本
            sendTTSWithText(text);
        }



        // 清空所有文字
        function clearAllText() {
            // 清空主输入框
            document.getElementById('textInput').value = '';
            updateCharCount();

            // 清空所有动态输入框
            const dynamicInputs = document.getElementById('dynamicInputs');
            const textareas = dynamicInputs.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                textarea.value = '';
                // 更新对应的字符计数
                const countId = textarea.id.replace('textarea_', 'count_');
                updateCharCount(textarea.id, countId);
            });
        }
        
        // 显示状态消息
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }
        
        // 测试连接
        async function testConnection() {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            const testBtn = document.getElementById('testBtn');
            
            testBtn.disabled = true;
            showStatus('正在测试连接...', 'info');
            
            try {
                const response = await fetch(serverUrl + '/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showStatus(`✅ 连接成功！服务: ${data.service}`, 'success');
                } else {
                    showStatus(`❌ 连接失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ 连接失败: ${error.message}`, 'error');
            } finally {
                testBtn.disabled = false;
            }
        }
        
        // 切换模式
        async function toggleMode() {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            const modeBtn = document.getElementById('modeBtn');

            modeBtn.disabled = true;
            showStatus('正在切换模式...', 'info');

            try {
                const response = await fetch(serverUrl + '/mode', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ mode: 'toggle' })
                });

                const data = await response.json();

                if (response.ok && data.status === 'success') {
                    showStatus(`✅ ${data.message}`, 'success');
                    updateModeDisplay(data.mode);
                } else {
                    showStatus(`❌ 切换失败: ${data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ 切换失败: ${error.message}`, 'error');
            } finally {
                modeBtn.disabled = false;
            }
        }

        // 检查待回复内容
        async function checkPending() {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            const pendingBtn = document.getElementById('pendingBtn');

            pendingBtn.disabled = true;

            try {
                const response = await fetch(serverUrl + '/pending', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });

                const data = await response.json();

                if (response.ok && data.status === 'success') {
                    updateModeDisplay(data.mode);
                    updatePendingDisplay(data.pending_text, data.has_pending);

                    if (data.has_pending) {
                        showStatus(`📝 有待回复内容: ${data.pending_text}`, 'info');
                        // 自动填入文本框
                        document.getElementById('textInput').value = `回复: ${data.pending_text}`;
                        updateCharCount();
                    } else {
                        showStatus('📝 暂无待回复内容', 'info');
                    }
                } else {
                    showStatus(`❌ 检查失败: ${data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ 检查失败: ${error.message}`, 'error');
            } finally {
                pendingBtn.disabled = false;
            }
        }

        // 更新模式显示
        function updateModeDisplay(mode) {
            const modeStatus = document.getElementById('modeStatus');
            const currentMode = document.getElementById('currentMode');
            const modeBtn = document.getElementById('modeBtn');

            currentMode.textContent = mode === 'manual' ? '手动回复模式' : '自动对话模式';
            modeStatus.style.display = 'block';

            if (mode === 'manual') {
                modeBtn.textContent = '🤖 切换到自动模式';
                modeStatus.className = 'status info';
            } else {
                modeBtn.textContent = '✋ 切换到手动模式';
                modeStatus.className = 'status success';
            }
        }

        // 更新待回复显示
        function updatePendingDisplay(text, hasPending) {
            const pendingStatus = document.getElementById('pendingStatus');
            const pendingText = document.getElementById('pendingText');

            if (hasPending && text) {
                pendingText.textContent = text;
                pendingStatus.className = 'status info';
                pendingStatus.style.display = 'block';
            } else {
                pendingText.textContent = '无';
                pendingStatus.style.display = 'none';
            }
        }

        // 发送TTS请求（原有函数，用于主输入框）
        async function sendTTS() {
            const text = document.getElementById('textInput').value.trim();
            sendTTSWithText(text);
        }

        // 发送TTS请求的核心逻辑
        async function sendTTSWithText(text) {
            const serverUrl = document.getElementById('serverUrl').value.trim();
            const sendBtn = document.getElementById('sendBtn');

            if (!text) {
                showStatus('❌ 请输入要合成的文字', 'error');
                return;
            }

            const byteLength = new TextEncoder().encode(text).length;
            if (byteLength > 1000) {
                showStatus('❌ 文字太长，超过1000字节限制', 'error');
                return;
            }

            // 禁用所有发送按钮
            const allSendBtns = document.querySelectorAll('.send-btn, #sendBtn');
            allSendBtns.forEach(btn => btn.disabled = true);

            showStatus('正在发送语音合成请求...', 'info');

            try {
                const response = await fetch(serverUrl + '/tts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ text: text })
                });

                const data = await response.json();

                if (response.ok && data.status === 'success') {
                    showStatus('✅ 语音合成请求发送成功！机器人应该开始说话了', 'success');
                    // 发送成功后清空待回复显示
                    updatePendingDisplay('', false);
                } else {
                    showStatus(`❌ 请求失败: ${data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ 请求失败: ${error.message}`, 'error');
            } finally {
                // 重新启用所有发送按钮
                allSendBtns.forEach(btn => btn.disabled = false);
            }
        }
        
        // 监听主输入框文字输入变化
        document.getElementById('textInput').addEventListener('input', () => updateCharCount());

        // 支持主输入框回车键发送
        document.getElementById('textInput').addEventListener('keydown', function(event) {
            if (event.ctrlKey && event.key === 'Enter') {
                sendTTS();
            }
        });

        // 初始化
        updateCharCount();

        // 页面加载完成后自动测试连接和获取状态
        window.addEventListener('load', function() {
            setTimeout(async function() {
                await testConnection();
                await checkPending();
            }, 1000);
        });
    </script>
</body>
</html>
