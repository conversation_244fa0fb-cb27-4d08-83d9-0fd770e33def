2025-08-05 14:31:52.733 - chat_with_robot - chat_with_robot.py - <module> - line 921 - INFO - use_action: dont
2025-08-05 14:31:52.734 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 14:31:52.740 - chat_with_robot - chat_with_robot.py - init_websocket - line 533 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754375512740&accessNonce=775bb338-805c-42fd-beaa-7ff72d324a33&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=dfe670f6-71c5-11f0-aa5e-dc4546c07870&requestId=24d6c959-257c-4d02-97d9-fb35de189fef_joyinside&accessSign=f47d68b745a06968482b9b074d8b2c6a, request_id: 24d6c959-257c-4d02-97d9-fb35de189fef_joyinside
2025-08-05 14:31:52.742 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 14:31:52.742 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 14:31:53.449 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 14:31:53.617 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-05 14:31:55.353 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-05 14:31:55.354 - chat_with_robot - voice.py - _setup_audio_stream - line 339 - INFO - 使用音频设备: 1
2025-08-05 14:31:55.354 - chat_with_robot - voice.py - _setup_audio_stream - line 340 - INFO - channels: 4 <class 'int'>
2025-08-05 14:31:55.354 - chat_with_robot - voice.py - _setup_audio_stream - line 341 - INFO - rate: 44100.0 <class 'float'>
2025-08-05 14:31:55.402 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-05 14:31:55.402 - chat_with_robot - voice.py - init_wakeup - line 326 - INFO - 本地流式KWS检测器启动成功
2025-08-05 14:31:56.404 - chat_with_robot - chat_with_robot.py - play_audio - line 796 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 14:31:56.404 - chat_with_robot - chat_with_robot.py - play_audio - line 804 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 14:31:59.406 - chat_with_robot - chat_with_robot.py - play_audio - line 806 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 14:31:59.406 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 261 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-08-05 14:31:59.406 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 264 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-08-05 14:31:59.413 - chat_with_robot - chat_with_robot.py - start_http_server - line 488 - INFO - HTTP API服务器已启动，监听端口: 8080
2025-08-05 14:31:59.413 - chat_with_robot - chat_with_robot.py - start_http_server - line 489 - INFO - API地址: http://localhost:8080/tts
2025-08-05 14:31:59.413 - chat_with_robot - chat_with_robot.py - start_http_server - line 490 - INFO - 使用示例: curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{"text":"你好，我是机器人"}'
2025-08-05 14:32:00.338 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS / HTTP/1.1" 200 -
2025-08-05 14:32:00.593 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "GET / HTTP/1.1" 200 -
2025-08-05 14:32:00.647 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /pending HTTP/1.1" 200 -
2025-08-05 14:32:00.913 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /pending HTTP/1.1" 200 -
2025-08-05 14:32:05.899 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /mode HTTP/1.1" 200 -
2025-08-05 14:32:06.210 - chat_with_robot - chat_with_robot.py - toggle_manual_reply_mode - line 568 - INFO - 已切换到: 手动回复模式
2025-08-05 14:32:06.210 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /mode HTTP/1.1" 200 -
2025-08-05 14:32:10.965 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:32:10.965 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 14:32:11.026 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:32:11.027 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:32:11.027 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:32:11.027 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 14:32:11.027 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 14:32:11.028 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 14:32:11.028 - chat_with_robot - voice.py - detect_callback - line 486 - INFO - 🔧 手动回复模式 - 等待手动输入回复内容
2025-08-05 14:32:11.032 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:32:11.032 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:32:11.132 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:32:15.964 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 14:32:16.271 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 561 - INFO - 发送文字转语音: 你好，我是机器人
2025-08-05 14:32:16.271 - chat_with_robot - chat_with_robot.py - do_POST - line 112 - INFO - 🔧 手动回复完成，重置为监听状态
2025-08-05 14:32:16.272 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 14:32:16.636 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 14:32:15.810000
2025-08-05 14:32:16.636 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1754375535810
2025-08-05 14:32:16.638 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:32:16.645 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:32:16.645 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:32:16.645 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:32:16.645 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 4536 bytes, 持续时间: 0.27685546875
2025-08-05 14:32:16.645 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:32:16.946 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:32:16.948 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:32:17.460 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:32:17.460 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:32:17.460 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:32:17.461 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:32:17.461 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 6480 bytes, 持续时间: 0.3955078125
2025-08-05 14:32:17.461 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:32:18.579 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:32:18.579 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:32:18.579 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:32:27.062 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 14:32:27.375 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 561 - INFO - 发送文字转语音: 欢迎使用文字转语音功能，请随时告诉我您的需求
2025-08-05 14:32:27.375 - chat_with_robot - chat_with_robot.py - do_POST - line 112 - INFO - 🔧 手动回复完成，重置为监听状态
2025-08-05 14:32:27.375 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 14:32:27.685 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:32:27.691 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:32:27.691 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:32:27.691 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:32:27.691 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:32:27.693 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11448 bytes, 持续时间: 2.0
2025-08-05 14:32:27.693 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:32:27.984 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:32:27.991 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:32:30.114 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:32:30.115 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:32:30.115 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:32:30.115 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:32:30.115 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:32:30.115 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 10800 bytes, 持续时间: 2.0
2025-08-05 14:32:30.115 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:32:32.541 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:32:32.541 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:32:32.541 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:33:03.112 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /mode HTTP/1.1" 200 -
2025-08-05 14:33:03.431 - chat_with_robot - chat_with_robot.py - toggle_manual_reply_mode - line 568 - INFO - 已切换到: 自动对话模式
2025-08-05 14:33:03.431 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /mode HTTP/1.1" 200 -
