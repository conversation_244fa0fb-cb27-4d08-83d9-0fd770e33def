2025-08-05 14:33:24.465 - chat_with_robot - chat_with_robot.py - <module> - line 921 - INFO - use_action: dont
2025-08-05 14:33:24.465 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 14:33:24.473 - chat_with_robot - chat_with_robot.py - init_websocket - line 533 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754375604472&accessNonce=056dcaf7-fb58-4b98-9637-2f62ff73ec80&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=16939a2e-71c6-11f0-ab2f-dc4546c07870&requestId=62904b36-f04a-44be-9656-ce8c0d3c3cf8_joyinside&accessSign=ba205447a483a4038f194e0550693bc0, request_id: 62904b36-f04a-44be-9656-ce8c0d3c3cf8_joyinside
2025-08-05 14:33:24.474 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 14:33:24.475 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 14:33:24.981 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 14:33:25.009 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-05 14:33:26.802 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-05 14:33:26.804 - chat_with_robot - voice.py - _setup_audio_stream - line 339 - INFO - 使用音频设备: 1
2025-08-05 14:33:26.804 - chat_with_robot - voice.py - _setup_audio_stream - line 340 - INFO - channels: 4 <class 'int'>
2025-08-05 14:33:26.804 - chat_with_robot - voice.py - _setup_audio_stream - line 341 - INFO - rate: 44100.0 <class 'float'>
2025-08-05 14:33:26.862 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-05 14:33:26.862 - chat_with_robot - voice.py - init_wakeup - line 326 - INFO - 本地流式KWS检测器启动成功
2025-08-05 14:33:27.862 - chat_with_robot - chat_with_robot.py - play_audio - line 796 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 14:33:27.862 - chat_with_robot - chat_with_robot.py - play_audio - line 804 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 14:33:29.282 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:33:29.282 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 14:33:29.345 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:33:29.346 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:33:29.346 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:33:29.355 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:33:29.355 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:33:29.456 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:33:30.865 - chat_with_robot - chat_with_robot.py - play_audio - line 806 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 14:33:30.865 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 261 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-08-05 14:33:30.866 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 264 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-08-05 14:33:30.870 - chat_with_robot - chat_with_robot.py - start_http_server - line 488 - INFO - HTTP API服务器已启动，监听端口: 8080
2025-08-05 14:33:30.870 - chat_with_robot - chat_with_robot.py - start_http_server - line 489 - INFO - API地址: http://localhost:8080/tts
2025-08-05 14:33:30.870 - chat_with_robot - chat_with_robot.py - start_http_server - line 490 - INFO - 使用示例: curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{"text":"你好，我是机器人"}'
2025-08-05 14:33:32.347 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:33:32.347 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:33:32.349 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 14:33:32.349 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 14:33:32.379 - chat_with_robot - voice.py - run - line 522 - INFO - [run] 持续监听状态...
2025-08-05 14:33:33.695 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 14:33:33.696 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 14:33:41.356 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:33:41.356 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 14:33:41.419 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:33:41.419 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:33:41.419 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:33:41.419 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 14:33:41.419 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 14:33:41.419 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 14:33:41.419 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 14:33:41.429 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:33:41.429 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:33:41.479 - chat_with_robot - voice.py - run - line 522 - INFO - [run] 持续监听状态...
2025-08-05 14:33:41.531 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:33:41.717 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-08-05 14:33:40.931000
2025-08-05 14:33:41.998 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-08-05 14:33:42.000 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:33:42.009 - chat_with_robot - chat_with_robot.py - _task_worker - line 644 - INFO - session_id: 16939a2e-71c6-11f0-ab2f-dc4546c07870; requestId: 62904b36-f04a-44be-9656-ce8c0d3c3cf8_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-08-05 14:33:42.009 - chat_with_robot - chat_with_robot.py - _task_worker - line 647 - INFO - 等待音频播放完成
2025-08-05 14:33:42.009 - chat_with_robot - chat_with_robot.py - _task_worker - line 662 - INFO - 任务完成，继续
2025-08-05 14:33:44.304 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:33:44.304 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 14:33:44.369 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:33:44.369 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:33:44.369 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:33:44.369 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:33:44.369 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:33:44.370 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 14:33:44.370 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 14:33:44.377 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:33:44.377 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:33:44.428 - chat_with_robot - voice.py - run - line 522 - INFO - [run] 持续监听状态...
2025-08-05 14:33:44.479 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:33:44.669 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-08-05 14:33:43.884000
2025-08-05 14:33:44.929 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-08-05 14:33:44.929 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:33:44.930 - chat_with_robot - chat_with_robot.py - _task_worker - line 644 - INFO - session_id: 16939a2e-71c6-11f0-ab2f-dc4546c07870; requestId: 62904b36-f04a-44be-9656-ce8c0d3c3cf8_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-08-05 14:33:44.930 - chat_with_robot - chat_with_robot.py - _task_worker - line 647 - INFO - 等待音频播放完成
2025-08-05 14:33:44.930 - chat_with_robot - chat_with_robot.py - _task_worker - line 662 - INFO - 任务完成，继续
2025-08-05 14:33:47.659 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下吧, 时间戳: 2025-08-05 14:33:46.876000
2025-08-05 14:33:47.665 - chat_with_robot - chat_with_robot.py - play_audio - line 796 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 14:33:47.666 - chat_with_robot - chat_with_robot.py - play_audio - line 804 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 14:33:47.667 - chat_with_robot - chat_with_robot.py - play_audio - line 806 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 14:33:48.610 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 14:33:47.802000
2025-08-05 14:33:48.610 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 926
2025-08-05 14:33:48.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:33:48.984 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:33:48.985 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:34:23.741 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS / HTTP/1.1" 200 -
2025-08-05 14:34:23.991 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "GET / HTTP/1.1" 200 -
2025-08-05 14:34:24.053 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /pending HTTP/1.1" 200 -
2025-08-05 14:34:24.302 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /pending HTTP/1.1" 200 -
2025-08-05 14:34:51.876 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:34:51.877 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 14:34:51.941 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:34:51.941 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:34:51.941 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:34:51.941 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:34:51.941 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:34:51.942 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 14:34:51.942 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 14:34:51.949 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:34:51.949 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:34:52.014 - chat_with_robot - voice.py - run - line 522 - INFO - [run] 持续监听状态...
2025-08-05 14:34:52.051 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:34:56.625 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我想知道今天成都的天气, 时间戳: 2025-08-05 14:34:55.842000
2025-08-05 14:34:58.238 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 14:34:57.438000
2025-08-05 14:34:58.239 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1596
2025-08-05 14:34:58.259 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:34:58.260 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:34:58.260 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:34:58.260 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:34:58.260 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:34:58.261 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 14472 bytes, 持续时间: 2.0
2025-08-05 14:34:58.261 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:34:58.536 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:34:58.538 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:34:58.904 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:34:58.914 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:34:59.215 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:34:59.216 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:34:59.477 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:34:59.484 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:34:59.777 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:34:59.778 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:34:59.786 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:34:59.797 - chat_with_robot - chat_with_robot.py - _task_worker - line 644 - INFO - session_id: 16939a2e-71c6-11f0-ab2f-dc4546c07870; requestId: 62904b36-f04a-44be-9656-ce8c0d3c3cf8_joyinside; asr: ，我想知道今天成都的天气; 响应时间: 0; JD机器人回复: 今天成都阴，温度和昨天差不多，现在38度，有风，空气一般。体感温度41度，相对湿度45%，北风3级，紫外线中等，能见度10公里，大气压944百帕。适合外出散步，但注意防晒。
2025-08-05 14:34:59.797 - chat_with_robot - chat_with_robot.py - _task_worker - line 647 - INFO - 等待音频播放完成
2025-08-05 14:35:00.688 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:35:00.688 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:35:00.689 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:35:00.689 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:35:00.689 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:35:00.689 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 14904 bytes, 持续时间: 2.0
2025-08-05 14:35:00.689 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:35:02.207 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:35:02.207 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 14:35:02.268 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:35:02.268 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:35:02.268 - chat_with_robot - chat_with_robot.py - _task_worker - line 662 - INFO - 任务完成，继续
2025-08-05 14:35:02.268 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:35:02.269 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:35:02.269 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:35:02.269 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:35:02.269 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:35:02.269 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:35:02.269 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 14:35:02.269 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 14:35:02.274 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:35:02.274 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:35:02.329 - chat_with_robot - voice.py - run - line 522 - INFO - [run] 持续监听状态...
2025-08-05 14:35:02.375 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:35:02.814 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东, 时间戳: 2025-08-05 14:35:02.031000
2025-08-05 14:35:03.339 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-08-05 14:35:03.340 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:35:03.350 - chat_with_robot - chat_with_robot.py - _task_worker - line 644 - INFO - session_id: 16939a2e-71c6-11f0-ab2f-dc4546c07870; requestId: 62904b36-f04a-44be-9656-ce8c0d3c3cf8_joyinside; asr: ，你好，东东; 响应时间: 0; JD机器人回复: 
2025-08-05 14:35:03.350 - chat_with_robot - chat_with_robot.py - _task_worker - line 647 - INFO - 等待音频播放完成
2025-08-05 14:35:03.350 - chat_with_robot - chat_with_robot.py - _task_worker - line 662 - INFO - 任务完成，继续
2025-08-05 14:35:05.069 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 14:35:06.030 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 14:35:06.030 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 14:35:49.959 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 14:35:49.960 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
