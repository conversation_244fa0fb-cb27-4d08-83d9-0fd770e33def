2025-08-05 14:20:31.938 - chat_with_robot - chat_with_robot.py - <module> - line 800 - INFO - use_action: dont
2025-08-05 14:20:31.939 - chat_with_robot - chat_with_robot.py - <module> - line 801 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 14:20:31.945 - chat_with_robot - chat_with_robot.py - init_websocket - line 451 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754374831946&accessNonce=04376e67-f66e-4910-bd3f-f615a38dfbcc&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=4a1d557e-71c4-11f0-9269-dc4546c07870&requestId=0ee431e5-b941-4267-aa9a-7b0c65c61afd_joyinside&accessSign=7540ab80feaaf9ceeb2b2bfa16942073, request_id: 0ee431e5-b941-4267-aa9a-7b0c65c61afd_joyinside
2025-08-05 14:20:31.946 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 14:20:31.946 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 14:20:32.530 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 14:20:32.635 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-05 14:20:34.107 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-05 14:20:34.107 - chat_with_robot - voice.py - _setup_audio_stream - line 336 - INFO - 使用音频设备: 1
2025-08-05 14:20:34.108 - chat_with_robot - voice.py - _setup_audio_stream - line 337 - INFO - channels: 4 <class 'int'>
2025-08-05 14:20:34.108 - chat_with_robot - voice.py - _setup_audio_stream - line 338 - INFO - rate: 44100.0 <class 'float'>
2025-08-05 14:20:34.162 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-05 14:20:34.162 - chat_with_robot - voice.py - init_wakeup - line 323 - INFO - 本地流式KWS检测器启动成功
2025-08-05 14:20:35.163 - chat_with_robot - chat_with_robot.py - play_audio - line 675 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 14:20:35.165 - chat_with_robot - chat_with_robot.py - play_audio - line 683 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 14:20:38.167 - chat_with_robot - chat_with_robot.py - play_audio - line 685 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 14:20:38.167 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 182 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-08-05 14:20:38.169 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 185 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-08-05 14:20:38.175 - chat_with_robot - chat_with_robot.py - start_http_server - line 406 - INFO - HTTP API服务器已启动，监听端口: 8080
2025-08-05 14:20:38.175 - chat_with_robot - chat_with_robot.py - start_http_server - line 407 - INFO - API地址: http://localhost:8080/tts
2025-08-05 14:20:38.175 - chat_with_robot - chat_with_robot.py - start_http_server - line 408 - INFO - 使用示例: curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{"text":"你好，我是机器人"}'
2025-08-05 14:20:42.619 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "OPTIONS / HTTP/1.1" 200 -
2025-08-05 14:20:42.884 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "GET / HTTP/1.1" 200 -
2025-08-05 14:20:54.540 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 14:20:54.855 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 479 - INFO - 发送文字转语音: 你好，我是机器人
2025-08-05 14:20:54.855 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 14:20:55.310 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 14:20:54.530000
2025-08-05 14:20:55.311 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1754374854530
2025-08-05 14:20:55.346 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:20:55.351 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:20:55.351 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:20:55.351 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:20:55.351 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 4968 bytes, 持续时间: 0.30322265625
2025-08-05 14:20:55.351 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:20:55.594 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:20:55.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:20:56.215 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:20:56.215 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:20:56.215 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:20:56.215 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:20:56.215 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 6480 bytes, 持续时间: 0.3955078125
2025-08-05 14:20:56.215 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:20:57.334 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:20:57.334 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:20:57.335 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:20:58.597 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 14:20:58.910 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 479 - INFO - 发送文字转语音: 今天天气真不错，适合出去走走
2025-08-05 14:20:58.910 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 14:20:59.237 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:20:59.249 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:20:59.249 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:20:59.249 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:20:59.249 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:20:59.249 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 8424 bytes, 持续时间: 2.0
2025-08-05 14:20:59.250 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:20:59.493 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:20:59.494 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:21:01.669 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:21:01.669 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:21:01.669 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:21:01.669 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:21:01.669 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 7776 bytes, 持续时间: 0.474609375
2025-08-05 14:21:01.669 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:21:02.992 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:21:02.993 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:21:02.993 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:21:04.343 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 14:21:04.661 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 479 - INFO - 发送文字转语音: 任务已完成，请查看结果
2025-08-05 14:21:04.661 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 14:21:04.984 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:21:04.988 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:21:04.988 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:21:04.988 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:21:04.988 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 6480 bytes, 持续时间: 0.3955078125
2025-08-05 14:21:04.988 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:21:05.267 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:21:05.275 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:21:06.106 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:21:06.107 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:21:06.107 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:21:06.108 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:21:06.108 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 7344 bytes, 持续时间: 0.4482421875
2025-08-05 14:21:06.108 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:21:07.378 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:21:07.378 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:21:07.378 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:21:12.763 - chat_with_robot - voice.py - detect_callback - line 438 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:21:12.763 - chat_with_robot - voice.py - end_streaming - line 237 - INFO - [end recording]...
2025-08-05 14:21:12.826 - chat_with_robot - voice.py - detect_callback - line 449 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:21:12.826 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:21:12.826 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:21:12.827 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:21:12.827 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:21:12.827 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 14:21:12.828 - chat_with_robot - voice.py - start_streaming - line 233 - INFO - [start recording]...
2025-08-05 14:21:12.836 - chat_with_robot - voice.py - run - line 512 - INFO - [run] 持续监听状态...
2025-08-05 14:21:12.836 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:21:12.836 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:21:12.936 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 500 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:21:14.799 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好, 时间戳: 2025-08-05 14:21:14.018000
2025-08-05 14:21:14.993 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-08-05 14:21:14.995 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:21:14.998 - chat_with_robot - chat_with_robot.py - _task_worker - line 540 - INFO - session_id: 4a1d557e-71c4-11f0-9269-dc4546c07870; requestId: 0ee431e5-b941-4267-aa9a-7b0c65c61afd_joyinside; asr: 你好; 响应时间: 0; JD机器人回复: 你好，我是机器人今天天气真不错，适合出去走走任务已完成，请查看结果
2025-08-05 14:21:14.998 - chat_with_robot - chat_with_robot.py - _task_worker - line 543 - INFO - 等待音频播放完成
2025-08-05 14:21:14.998 - chat_with_robot - chat_with_robot.py - _task_worker - line 558 - INFO - 任务完成，继续
2025-08-05 14:21:17.095 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我想知道今天天气, 时间戳: 2025-08-05 14:21:16.316000
2025-08-05 14:21:18.213 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 14:21:17.410000
2025-08-05 14:21:18.213 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1094
2025-08-05 14:21:18.222 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:21:18.224 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:21:18.224 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:21:18.224 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:21:18.224 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:21:18.224 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 14472 bytes, 持续时间: 2.0
2025-08-05 14:21:18.224 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:21:18.581 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:21:18.582 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:21:18.634 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 14:21:18.944 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 479 - INFO - 发送文字转语音: 你好，我是机器人
2025-08-05 14:21:18.945 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 14:21:18.987 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-08-05 14:21:18.989 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:21:18.989 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:21:18.990 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:21:19.000 - chat_with_robot - chat_with_robot.py - _task_worker - line 540 - INFO - session_id: 4a1d557e-71c4-11f0-9269-dc4546c07870; requestId: 0ee431e5-b941-4267-aa9a-7b0c65c61afd_joyinside; asr: ，我想知道今天天气; 响应时间: 0; JD机器人回复: 今天北京白天多云，夜晚多云，比昨天热一些，现在气温31度，
2025-08-05 14:21:19.000 - chat_with_robot - chat_with_robot.py - _task_worker - line 543 - INFO - 等待音频播放完成
2025-08-05 14:21:19.000 - chat_with_robot - chat_with_robot.py - _task_worker - line 558 - INFO - 任务完成，继续
2025-08-05 14:21:19.091 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 500 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:21:19.205 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 14:21:18.424000
2025-08-05 14:21:19.206 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 2108
2025-08-05 14:21:19.236 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:21:19.239 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:21:19.542 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:21:19.550 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:21:20.658 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:21:20.659 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:21:20.659 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:21:20.659 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:21:20.659 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 4536 bytes, 持续时间: 0.27685546875
2025-08-05 14:21:20.659 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:21:21.427 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:21:21.427 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:21:21.428 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:21:21.428 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:21:21.428 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 6912 bytes, 持续时间: 0.421875
2025-08-05 14:21:21.428 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:21:22.598 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:21:22.598 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:21:22.598 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:21:24.653 - chat_with_robot - voice.py - detect_callback - line 438 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:21:24.653 - chat_with_robot - voice.py - end_streaming - line 237 - INFO - [end recording]...
2025-08-05 14:21:24.720 - chat_with_robot - voice.py - detect_callback - line 449 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:21:24.720 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:21:24.720 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:21:24.721 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 14:21:24.722 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 14:21:24.723 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 14:21:24.723 - chat_with_robot - voice.py - start_streaming - line 233 - INFO - [start recording]...
2025-08-05 14:21:24.728 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:21:24.728 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:21:24.775 - chat_with_robot - voice.py - run - line 512 - INFO - [run] 持续监听状态...
2025-08-05 14:21:24.829 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 500 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:21:25.325 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-08-05 14:21:24.546000
2025-08-05 14:21:26.832 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 14:21:26.946 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:21:26.956 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:21:26.956 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:21:26.956 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:21:26.956 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:21:26.956 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 16632 bytes, 持续时间: 2.0
2025-08-05 14:21:26.956 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:21:27.141 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 479 - INFO - 发送文字转语音: 今天天气真不错，适合出去走走
2025-08-05 14:21:27.141 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 14:21:27.185 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-08-05 14:21:27.186 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:21:27.191 - chat_with_robot - chat_with_robot.py - _task_worker - line 540 - INFO - session_id: 4a1d557e-71c4-11f0-9269-dc4546c07870; requestId: 0ee431e5-b941-4267-aa9a-7b0c65c61afd_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 你好，我是机器人你好，东东！今天北京的天气是多云，
2025-08-05 14:21:27.191 - chat_with_robot - chat_with_robot.py - _task_worker - line 543 - INFO - 等待音频播放完成
2025-08-05 14:21:27.191 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:21:27.191 - chat_with_robot - chat_with_robot.py - _task_worker - line 558 - INFO - 任务完成，继续
2025-08-05 14:21:27.191 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:21:27.293 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 500 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:21:27.422 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 14:21:26.634000
2025-08-05 14:21:27.422 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 2088
2025-08-05 14:21:27.447 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:21:27.450 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:21:27.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:21:27.755 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:21:29.792 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:21:29.792 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:21:29.792 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:21:29.792 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:21:29.792 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:21:29.792 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 8208 bytes, 持续时间: 2.0
2025-08-05 14:21:29.792 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:21:30.056 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 14:21:30.363 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 479 - INFO - 发送文字转语音: 今天天气真不错，适合出去走走
2025-08-05 14:21:30.363 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 14:21:30.662 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:21:30.671 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:21:30.990 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:21:30.992 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:21:32.213 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:21:32.213 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:21:32.214 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:21:32.214 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:21:32.214 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 7776 bytes, 持续时间: 0.474609375
2025-08-05 14:21:32.214 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:21:33.538 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:21:33.538 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:21:33.538 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:21:33.538 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:21:33.539 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:21:33.539 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 8640 bytes, 持续时间: 2.0
2025-08-05 14:21:33.539 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:21:35.961 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:21:35.961 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:21:35.961 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:21:35.961 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:21:35.962 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 7344 bytes, 持续时间: 0.4482421875
2025-08-05 14:21:35.962 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:21:37.187 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:21:37.187 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:21:37.187 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:22:36.684 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:22:36.690 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:22:36.690 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:22:36.690 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:22:36.690 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:22:36.690 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 16632 bytes, 持续时间: 2.0
2025-08-05 14:22:36.690 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:22:37.057 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:22:37.064 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:22:37.389 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:22:37.394 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:22:37.682 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:22:37.686 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:22:37.943 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:22:37.945 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:22:38.254 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:22:38.255 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:22:38.259 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:22:38.270 - chat_with_robot - chat_with_robot.py - _task_worker - line 540 - INFO - session_id: 4a1d557e-71c4-11f0-9269-dc4546c07870; requestId: 0ee431e5-b941-4267-aa9a-7b0c65c61afd_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 今天天气真不错，适合出去走走今天天气真不错，适合出去走走你好，东东！今天北京的天气是多云，白天最高气温33度，最低气温23度。现在的体感温度是34度，空气质量不错，相对湿度为72%，风力不大，东风2级。适合外出活动，不过记得防晒哦！
2025-08-05 14:22:38.270 - chat_with_robot - chat_with_robot.py - _task_worker - line 543 - INFO - 等待音频播放完成
2025-08-05 14:22:39.515 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:22:39.515 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:22:39.515 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:22:39.515 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:22:39.515 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:22:39.516 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11448 bytes, 持续时间: 2.0
2025-08-05 14:22:39.516 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:22:41.934 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:22:41.934 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:22:41.934 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:22:41.934 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:22:41.934 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:22:41.934 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 20736 bytes, 持续时间: 2.0
2025-08-05 14:22:41.934 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:22:42.072 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 14:22:42.336 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 479 - INFO - 发送文字转语音: 系统正在处理中，请稍等
2025-08-05 14:22:42.336 - chat_with_robot - chat_with_robot.py - log_message - line 150 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 14:22:42.658 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 14:22:41.871000
2025-08-05 14:22:42.659 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 77325
2025-08-05 14:22:42.665 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:22:42.665 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:22:42.665 - chat_with_robot - chat_with_robot.py - _task_worker - line 558 - INFO - 任务完成，继续
2025-08-05 14:22:42.685 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:22:42.688 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:22:42.766 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 492 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-08-05 14:22:42.978 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:22:42.989 - chat_with_robot - chat_with_robot.py - _task_worker - line 587 - INFO - 存入音频
2025-08-05 14:22:45.363 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:22:45.363 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:22:45.363 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:22:45.363 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:22:45.363 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:22:45.363 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 8424 bytes, 持续时间: 2.0
2025-08-05 14:22:45.363 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:22:47.783 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:22:47.785 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:22:47.785 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:22:47.785 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:22:47.785 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 5184 bytes, 持续时间: 0.31640625
2025-08-05 14:22:47.786 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:22:48.651 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:22:48.651 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:22:48.651 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:23:24.699 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 14:23:24.699 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 14:23:25.783 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 14:23:25.783 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 14:23:26.373 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 14:23:26.374 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 14:23:28.295 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 14:23:28.295 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 14:28:19.191 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 14:28:19.193 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 14:29:29.882 - chat_with_robot - voice.py - detect_callback - line 438 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:29:29.882 - chat_with_robot - voice.py - end_streaming - line 237 - INFO - [end recording]...
2025-08-05 14:29:29.945 - chat_with_robot - voice.py - detect_callback - line 449 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:29:29.945 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:29:29.945 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:29:29.945 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 14:29:29.948 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:29:29.948 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 14:29:29.948 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:29:29.948 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 14:29:29.948 - chat_with_robot - voice.py - start_streaming - line 233 - INFO - [start recording]...
2025-08-05 14:29:30.007 - chat_with_robot - voice.py - run - line 512 - INFO - [run] 持续监听状态...
2025-08-05 14:29:30.050 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 500 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:29:34.870 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下吧, 时间戳: 2025-08-05 14:29:34.087000
2025-08-05 14:29:34.873 - chat_with_robot - chat_with_robot.py - play_audio - line 675 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 14:29:34.873 - chat_with_robot - chat_with_robot.py - play_audio - line 683 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 14:29:34.875 - chat_with_robot - chat_with_robot.py - play_audio - line 685 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 14:29:36.133 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:29:36.472 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:29:36.760 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:29:36.761 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:30:33.613 - chat_with_robot - voice.py - detect_callback - line 438 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:30:33.613 - chat_with_robot - voice.py - end_streaming - line 237 - INFO - [end recording]...
2025-08-05 14:30:33.677 - chat_with_robot - voice.py - detect_callback - line 449 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:30:33.677 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:30:33.677 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:30:33.677 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 14:30:33.677 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 14:30:33.678 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 14:30:33.678 - chat_with_robot - voice.py - start_streaming - line 233 - INFO - [start recording]...
2025-08-05 14:30:33.680 - chat_with_robot - voice.py - run - line 512 - INFO - [run] 持续监听状态...
2025-08-05 14:30:33.681 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:30:33.681 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:30:33.782 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 500 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:30:53.314 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-08-05 14:30:53.314 - chat_with_robot - voice.py - stop - line 433 - INFO - 已停止local_streaming检测器
