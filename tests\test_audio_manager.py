"""
音频管理器测试
"""
import pytest
import time
from unittest.mock import Mock, patch
from core.audio_manager import AudioManager

class TestAudioManager:
    
    @pytest.fixture
    def audio_manager(self):
        """创建音频管理器实例"""
        return AudioManager()
        
    def test_start_recording(self, audio_manager):
        """测试开始录音"""
        callback = Mock()
        audio_manager.start_recording(callback)
        assert audio_manager.is_recording is True
        
    def test_stop_recording(self, audio_manager):
        """测试停止录音"""
        audio_manager.start_recording()
        audio_manager.stop_recording()
        assert audio_manager.is_recording is False
        
    def test_play_audio(self, audio_manager):
        """测试音频播放"""
        test_audio = b"test_audio_data"
        audio_manager.play_audio(test_audio)
        # 验证音频被添加到队列
        assert not audio_manager.playback_queue.empty()
        
    def test_priority_audio(self, audio_manager):
        """测试优先级音频"""
        # 添加普通音频
        audio_manager.play_audio(b"normal_audio")
        # 添加优先级音频
        audio_manager.play_audio(b"priority_audio", priority=True)
        # 验证队列被清空并添加了优先级音频
        assert audio_manager.playback_queue.qsize() == 1
        
    def test_cleanup(self, audio_manager):
        """测试资源清理"""
        audio_manager.cleanup()
        assert audio_manager._stop_event.is_set()