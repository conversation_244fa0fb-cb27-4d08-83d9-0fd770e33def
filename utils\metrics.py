"""
性能监控和指标收集
"""
import time
import threading
from typing import Dict, Any
from collections import defaultdict, deque
from util.logger import logger

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics = defaultdict(deque)
        self.counters = defaultdict(int)
        self.timers = {}
        self._lock = threading.Lock()
        
    def record_latency(self, operation: str, duration: float):
        """记录延迟指标"""
        with self._lock:
            self.metrics[f"{operation}_latency"].append({
                'timestamp': time.time(),
                'duration': duration
            })
            # 保持历史记录在限制范围内
            if len(self.metrics[f"{operation}_latency"]) > self.max_history:
                self.metrics[f"{operation}_latency"].popleft()
                
    def increment_counter(self, counter_name: str, value: int = 1):
        """增加计数器"""
        with self._lock:
            self.counters[counter_name] += value
            
    def start_timer(self, timer_name: str):
        """开始计时"""
        self.timers[timer_name] = time.time()
        
    def end_timer(self, timer_name: str) -> float:
        """结束计时并记录"""
        if timer_name not in self.timers:
            logger.warning(f"计时器 {timer_name} 未启动")
            return 0.0
            
        duration = time.time() - self.timers[timer_name]
        self.record_latency(timer_name, duration)
        del self.timers[timer_name]
        return duration
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            stats = {
                'counters': dict(self.counters),
                'latency_stats': {}
            }
            
            for operation, latencies in self.metrics.items():
                if latencies:
                    durations = [l['duration'] for l in latencies]
                    stats['latency_stats'][operation] = {
                        'count': len(durations),
                        'avg': sum(durations) / len(durations),
                        'min': min(durations),
                        'max': max(durations),
                        'p95': sorted(durations)[int(len(durations) * 0.95)] if durations else 0
                    }
                    
            return stats
            
    def log_stats(self):
        """记录统计信息到日志"""
        stats = self.get_stats()
        logger.info(f"性能统计: {stats}")

# 全局指标收集器
metrics = MetricsCollector()