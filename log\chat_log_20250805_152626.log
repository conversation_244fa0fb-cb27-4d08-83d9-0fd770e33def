2025-08-05 15:26:27.553 - chat_with_robot - chat_with_robot.py - <module> - line 921 - INFO - use_action: dont
2025-08-05 15:26:27.553 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 15:26:27.559 - chat_with_robot - chat_with_robot.py - init_websocket - line 533 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754378787559&accessNonce=fbcedc83-2615-4de5-b8d2-abb45d7354df&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=7fd831a1-71cd-11f0-9792-dc4546c07870&requestId=e33cbb2f-c241-4730-8b37-7c1c84a66a0b_joyinside&accessSign=c67615556c89d296108220d000671352, request_id: e33cbb2f-c241-4730-8b37-7c1c84a66a0b_joyinside
2025-08-05 15:26:27.560 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 15:26:27.560 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 15:26:27.957 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 15:26:28.420 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-05 15:26:30.223 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-05 15:26:30.224 - chat_with_robot - voice.py - _setup_audio_stream - line 339 - INFO - 使用音频设备: 1
2025-08-05 15:26:30.224 - chat_with_robot - voice.py - _setup_audio_stream - line 340 - INFO - channels: 4 <class 'int'>
2025-08-05 15:26:30.224 - chat_with_robot - voice.py - _setup_audio_stream - line 341 - INFO - rate: 44100.0 <class 'float'>
2025-08-05 15:26:30.306 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-05 15:26:30.306 - chat_with_robot - voice.py - init_wakeup - line 326 - INFO - 本地流式KWS检测器启动成功
2025-08-05 15:26:31.306 - chat_with_robot - chat_with_robot.py - play_audio - line 796 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 15:26:31.306 - chat_with_robot - chat_with_robot.py - play_audio - line 804 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 15:26:34.309 - chat_with_robot - chat_with_robot.py - play_audio - line 806 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 15:26:34.310 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 261 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-08-05 15:26:34.310 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 264 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-08-05 15:26:34.317 - chat_with_robot - chat_with_robot.py - start_http_server - line 488 - INFO - HTTP API服务器已启动，监听端口: 8080
2025-08-05 15:26:34.317 - chat_with_robot - chat_with_robot.py - start_http_server - line 489 - INFO - API地址: http://localhost:8080/tts
2025-08-05 15:26:34.317 - chat_with_robot - chat_with_robot.py - start_http_server - line 490 - INFO - 使用示例: curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{"text":"你好，我是机器人"}'
2025-08-05 15:26:34.550 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS / HTTP/1.1" 200 -
2025-08-05 15:26:34.832 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "GET / HTTP/1.1" 200 -
2025-08-05 15:26:34.863 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /pending HTTP/1.1" 200 -
2025-08-05 15:26:35.144 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /pending HTTP/1.1" 200 -
2025-08-05 15:26:39.927 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /mode HTTP/1.1" 200 -
2025-08-05 15:26:40.249 - chat_with_robot - chat_with_robot.py - toggle_manual_reply_mode - line 568 - INFO - 已切换到: 手动回复模式
2025-08-05 15:26:40.249 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /mode HTTP/1.1" 200 -
2025-08-05 15:27:21.762 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS / HTTP/1.1" 200 -
2025-08-05 15:27:22.084 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "GET / HTTP/1.1" 200 -
2025-08-05 15:27:22.333 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /pending HTTP/1.1" 200 -
2025-08-05 15:27:22.395 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /pending HTTP/1.1" 200 -
2025-08-05 15:27:28.570 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /mode HTTP/1.1" 200 -
2025-08-05 15:27:28.884 - chat_with_robot - chat_with_robot.py - toggle_manual_reply_mode - line 568 - INFO - 已切换到: 自动对话模式
2025-08-05 15:27:28.885 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /mode HTTP/1.1" 200 -
2025-08-05 15:28:05.968 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 15:28:06.280 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 561 - INFO - 发送文字转语音: 撒大苏打 阿松大阿松大阿大撒
2025-08-05 15:28:06.282 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 15:28:06.639 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 15:28:05.798000
2025-08-05 15:28:06.639 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1754378885798
2025-08-05 15:28:06.644 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 15:28:06.651 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 15:28:06.651 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 15:28:06.651 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 15:28:06.652 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 15:28:06.652 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13608 bytes, 持续时间: 2.0
2025-08-05 15:28:06.652 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 15:28:09.079 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 15:28:09.079 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 15:28:09.079 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 15:29:26.930 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS / HTTP/1.1" 200 -
2025-08-05 15:29:27.191 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "GET / HTTP/1.1" 200 -
2025-08-05 15:29:27.239 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /pending HTTP/1.1" 200 -
2025-08-05 15:29:29.136 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS / HTTP/1.1" 200 -
2025-08-05 15:29:29.141 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "GET / HTTP/1.1" 200 -
2025-08-05 15:29:29.460 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /pending HTTP/1.1" 200 -
2025-08-05 15:29:29.724 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /pending HTTP/1.1" 200 -
2025-08-05 15:29:31.391 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /mode HTTP/1.1" 200 -
2025-08-05 15:29:31.697 - chat_with_robot - chat_with_robot.py - toggle_manual_reply_mode - line 568 - INFO - 已切换到: 手动回复模式
2025-08-05 15:29:31.698 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /mode HTTP/1.1" 200 -
2025-08-05 15:29:34.799 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /mode HTTP/1.1" 200 -
2025-08-05 15:29:35.107 - chat_with_robot - chat_with_robot.py - toggle_manual_reply_mode - line 568 - INFO - 已切换到: 自动对话模式
2025-08-05 15:29:35.107 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /mode HTTP/1.1" 200 -
2025-08-05 15:29:37.046 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 15:29:37.046 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 15:29:37.110 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 15:29:37.110 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 15:29:37.110 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 15:29:37.111 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 15:29:37.111 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 15:29:37.111 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 15:29:37.111 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 15:29:37.121 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 15:29:37.121 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 15:29:37.122 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 15:29:37.223 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 15:29:38.468 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 15:29:38.468 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 15:29:41.142 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天成都天气怎么样？, 时间戳: 2025-08-05 15:29:40.329000
2025-08-05 15:29:42.593 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 15:29:42.604 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 15:29:42.605 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 15:29:42.605 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 15:29:42.605 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 15:29:42.606 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 14904 bytes, 持续时间: 2.0
2025-08-05 15:29:42.606 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 15:29:42.900 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 15:29:42.905 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 15:29:43.191 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 15:29:43.198 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 15:29:43.470 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 15:29:43.479 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 15:29:43.807 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 15:29:43.812 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 15:29:44.046 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 15:29:44.047 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 15:29:44.048 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 15:29:44.058 - chat_with_robot - chat_with_robot.py - _task_worker - line 644 - INFO - session_id: 7fd831a1-71cd-11f0-9792-dc4546c07870; requestId: e33cbb2f-c241-4730-8b37-7c1c84a66a0b_joyinside; asr: 今天成都天气怎么样？; 响应时间: 0; JD机器人回复: 撒大苏打 阿松大阿松大阿大撒今天成都阴，温度和昨天差不多，现在体感温度39度，有北风3级，相对湿度41%，紫外线中等，能见度10公里，空气一般。适合外出时带上伞，注意防晒。
2025-08-05 15:29:44.058 - chat_with_robot - chat_with_robot.py - _task_worker - line 647 - INFO - 等待音频播放完成
2025-08-05 15:29:45.136 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 15:29:45.137 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 15:29:45.137 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 15:29:45.137 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 15:29:45.137 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 15:29:45.137 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11016 bytes, 持续时间: 2.0
2025-08-05 15:29:45.138 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 15:29:45.763 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 15:29:45.763 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 15:29:45.820 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 15:29:45.820 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 15:29:45.821 - chat_with_robot - chat_with_robot.py - _task_worker - line 662 - INFO - 任务完成，继续
2025-08-05 15:29:45.821 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 15:29:45.822 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 15:29:45.821 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 15:29:45.822 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 15:29:45.822 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 15:29:45.823 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 15:29:45.823 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 15:29:45.823 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 15:29:45.824 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 15:29:45.824 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 15:29:45.881 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 15:29:45.922 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 15:29:46.381 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-08-05 15:29:45.566000
2025-08-05 15:29:46.754 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-08-05 15:29:46.756 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 15:29:46.758 - chat_with_robot - chat_with_robot.py - _task_worker - line 644 - INFO - session_id: 7fd831a1-71cd-11f0-9792-dc4546c07870; requestId: e33cbb2f-c241-4730-8b37-7c1c84a66a0b_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-08-05 15:29:46.759 - chat_with_robot - chat_with_robot.py - _task_worker - line 647 - INFO - 等待音频播放完成
2025-08-05 15:29:46.759 - chat_with_robot - chat_with_robot.py - _task_worker - line 662 - INFO - 任务完成，继续
2025-08-05 15:29:48.400 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下吧, 时间戳: 2025-08-05 15:29:47.588000
2025-08-05 15:29:48.402 - chat_with_robot - chat_with_robot.py - play_audio - line 796 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 15:29:48.402 - chat_with_robot - chat_with_robot.py - play_audio - line 804 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 15:29:48.403 - chat_with_robot - chat_with_robot.py - play_audio - line 806 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 15:29:49.574 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 15:29:48.745000
2025-08-05 15:29:49.575 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1157
2025-08-05 15:29:49.596 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 15:29:49.856 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 15:29:50.137 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 15:29:50.137 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 15:29:51.256 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /mode HTTP/1.1" 200 -
2025-08-05 15:29:51.574 - chat_with_robot - chat_with_robot.py - toggle_manual_reply_mode - line 568 - INFO - 已切换到: 手动回复模式
2025-08-05 15:29:51.575 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /mode HTTP/1.1" 200 -
2025-08-05 15:29:53.509 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 15:29:53.509 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 15:29:53.570 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 15:29:53.570 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 15:29:53.570 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 15:29:53.571 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 15:29:53.571 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 15:29:53.571 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 15:29:53.572 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 15:29:53.572 - chat_with_robot - voice.py - detect_callback - line 492 - INFO - 🔧 手动回复模式 - 等待手动输入回复内容
2025-08-05 15:29:53.579 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 15:29:53.579 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 15:29:53.681 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 15:30:12.089 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 15:30:12.393 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 561 - INFO - 发送文字转语音: 我是小布，你是谁？
2025-08-05 15:30:12.393 - chat_with_robot - chat_with_robot.py - do_POST - line 112 - INFO - 🔧 手动回复完成，重置为监听状态，等待下次唤醒
2025-08-05 15:30:12.393 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 15:30:12.697 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 15:30:11.878000
2025-08-05 15:30:12.697 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 24290
2025-08-05 15:30:12.726 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 15:30:12.733 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 15:30:12.733 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 15:30:12.733 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 15:30:12.733 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 5616 bytes, 持续时间: 0.3427734375
2025-08-05 15:30:12.733 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 15:30:13.104 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 15:30:13.113 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 15:30:13.700 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 15:30:13.700 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 15:30:13.700 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 15:30:13.700 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 15:30:13.700 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 5400 bytes, 持续时间: 0.32958984375
2025-08-05 15:30:13.700 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 15:30:14.617 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 15:30:14.617 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 15:30:14.617 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 15:30:24.767 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 15:30:25.086 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 561 - INFO - 发送文字转语音: 你是偷井盖的阿拉蕾
2025-08-05 15:30:25.086 - chat_with_robot - chat_with_robot.py - do_POST - line 112 - INFO - 🔧 手动回复完成，重置为监听状态，等待下次唤醒
2025-08-05 15:30:25.086 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 15:30:25.434 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 15:30:25.438 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 15:30:25.438 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 15:30:25.439 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 15:30:25.439 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 15:30:25.439 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 9936 bytes, 持续时间: 2.0
2025-08-05 15:30:25.439 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 15:30:27.860 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 15:30:27.860 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 15:30:27.860 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
