"""
配置管理器 - 支持热重载和多环境配置
"""
import os
import json
import yaml
import threading
import time
from typing import Dict, Any, Optional, Callable
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from utils.error_handler import handle_exceptions
from util.logger import logger

class ConfigChangeHandler(FileSystemEventHandler):
    """配置文件变更处理器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        
    def on_modified(self, event):
        if not event.is_directory:
            self.config_manager._reload_config(event.src_path)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置数据
        self.configs: Dict[str, Any] = {}
        self.config_files: Dict[str, str] = {}  # 文件路径映射
        
        # 变更回调
        self.change_callbacks: Dict[str, list] = {}
        
        # 文件监控
        self.observer = Observer()
        self.observer.start()
        
        # 锁
        self.config_lock = threading.RLock()
        
        # 环境变量
        self.env = os.getenv('ROBOT_ENV', 'development')
        
        # 加载配置
        self._load_all_configs()
        
    def _load_all_configs(self):
        """加载所有配置文件"""
        config_patterns = [
            f"config.{self.env}.json",
            f"config.{self.env}.yaml",
            f"config.{self.env}.yml",
            "config.json",
            "config.yaml",
            "config.yml"
        ]
        
        for pattern in config_patterns:
            config_file = self.config_dir / pattern
            if config_file.exists():
                self._load_config_file(str(config_file))
                
        # 监控配置目录
        self.observer.schedule(
            ConfigChangeHandler(self),
            str(self.config_dir),
            recursive=True
        )
        
    @handle_exceptions()
    def _load_config_file(self, file_path: str):
        """加载单个配置文件"""
        file_path = Path(file_path)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix.lower() in ['.yaml', '.yml']:
                    data = yaml.safe_load(f)
                else:
                    data = json.load(f)
                    
            with self.config_lock:
                config_name = file_path.stem
                self.configs[config_name] = data
                self.config_files[config_name] = str(file_path)
                
            logger.info(f"加载配置文件: {file_path}")
            
            # 触发变更回调
            self._trigger_callbacks(config_name, data)
            
        except Exception as e:
            logger.error(f"加载配置文件失败 {file_path}: {e}")
            
    def _reload_config(self, file_path: str):
        """重新加载配置文件"""
        logger.info(f"检测到配置文件变更: {file_path}")
        time.sleep(0.1)  # 避免文件写入未完成
        self._load_config_file(file_path)
        
    def get(self, key: str, default: Any = None, config_name: str = None) -> Any:
        """获取配置值"""
        with self.config_lock:
            if config_name:
                config = self.configs.get(config_name, {})
            else:
                # 按优先级查找
                config = {}
                for name in [f"config.{self.env}", "config"]:
                    if name in self.configs:
                        config.update(self.configs[name])
                        
            # 支持嵌套键 如 "database.host"
            keys = key.split('.')
            value = config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
                    
            return value
            
    def set(self, key: str, value: Any, config_name: str = "config", persist: bool = True):
        """设置配置值"""
        with self.config_lock:
            if config_name not in self.configs:
                self.configs[config_name] = {}
                
            # 支持嵌套键设置
            keys = key.split('.')
            config = self.configs[config_name]
            
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
                
            config[keys[-1]] = value
            
            # 持久化到文件
            if persist:
                self._save_config(config_name)
                
            # 触发回调
            self._trigger_callbacks(config_name, self.configs[config_name])
            
    def _save_config(self, config_name: str):
        """保存配置到文件"""
        try:
            if config_name in self.config_files:
                file_path = Path(self.config_files[config_name])
            else:
                file_path = self.config_dir / f"{config_name}.json"
                self.config_files[config_name] = str(file_path)
                
            with open(file_path, 'w', encoding='utf-8') as f:
                if file_path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(self.configs[config_name], f, default_flow_style=False)
                else:
                    json.dump(self.configs[config_name], f, indent=2, ensure_ascii=False)
                    
            logger.debug(f"配置已保存: {file_path}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            
    def register_callback(self, config_name: str, callback: Callable[[Dict], None]):
        """注册配置变更回调"""
        if config_name not in self.change_callbacks:
            self.change_callbacks[config_name] = []
        self.change_callbacks[config_name].append(callback)
        
    def _trigger_callbacks(self, config_name: str, config_data: Dict):
        """触发变更回调"""
        callbacks = self.change_callbacks.get(config_name, [])
        for callback in callbacks:
            try:
                callback(config_data)
            except Exception as e:
                logger.error(f"配置变更回调执行失败: {e}")
                
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有配置"""
        with self.config_lock:
            return self.configs.copy()
            
    def reload_all(self):
        """重新加载所有配置"""
        logger.info("重新加载所有配置")
        self._load_all_configs()
        
    def shutdown(self):
        """关闭配置管理器"""
        self.observer.stop()
        self.observer.join()
        logger.info("配置管理器已关闭")

# 全局配置管理器
config_manager = ConfigManager()