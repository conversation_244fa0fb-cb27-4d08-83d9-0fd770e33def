"""
资源管理器 - 统一管理内存、文件和连接资源
"""
import os
import gc
import tempfile
import threading
import weakref
from typing import Dict, List, Optional
from contextlib import contextmanager
from pathlib import Path
import psutil
from utils.error_handler import handle_exceptions
from util.logger import logger

class ResourceManager:
    """统一资源管理器"""
    
    def __init__(self, max_memory_mb: int = 512, max_temp_files: int = 100):
        self.max_memory_mb = max_memory_mb
        self.max_temp_files = max_temp_files
        
        # 临时文件跟踪
        self.temp_files: List[str] = []
        self.temp_files_lock = threading.Lock()
        
        # 音频缓冲区管理
        self.audio_buffers: Dict[str, bytes] = {}
        self.audio_buffers_lock = threading.Lock()
        
        # 弱引用跟踪活跃对象
        self.active_objects = weakref.WeakSet()
        
        # 启动资源监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self.monitor_running = True
        self.monitor_thread.start()
        
    @contextmanager
    def temp_file(self, suffix: str = '.tmp', prefix: str = 'robot_'):
        """临时文件上下文管理器"""
        temp_path = None
        try:
            # 创建临时文件
            fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix)
            os.close(fd)  # 关闭文件描述符，只保留路径
            
            with self.temp_files_lock:
                self.temp_files.append(temp_path)
                
            yield temp_path
            
        finally:
            # 自动清理
            if temp_path and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                    with self.temp_files_lock:
                        if temp_path in self.temp_files:
                            self.temp_files.remove(temp_path)
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {temp_path}, {e}")
                    
    @handle_exceptions()
    def cache_audio_buffer(self, key: str, data: bytes, max_size: int = 1024*1024):
        """缓存音频数据"""
        if len(data) > max_size:
            logger.warning(f"音频数据过大，跳过缓存: {len(data)} bytes")
            return False
            
        with self.audio_buffers_lock:
            # 检查内存使用
            if self._get_memory_usage() > self.max_memory_mb:
                self._cleanup_audio_buffers()
                
            self.audio_buffers[key] = data
            return True
            
    def get_audio_buffer(self, key: str) -> Optional[bytes]:
        """获取缓存的音频数据"""
        with self.audio_buffers_lock:
            return self.audio_buffers.get(key)
            
    def _cleanup_audio_buffers(self):
        """清理音频缓冲区"""
        # 清理最老的50%缓冲区
        keys_to_remove = list(self.audio_buffers.keys())[:len(self.audio_buffers)//2]
        for key in keys_to_remove:
            del self.audio_buffers[key]
        logger.info(f"清理了 {len(keys_to_remove)} 个音频缓冲区")
        
    def _get_memory_usage(self) -> float:
        """获取当前进程内存使用量(MB)"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0
            
    def _monitor_resources(self):
        """资源监控线程"""
        while self.monitor_running:
            try:
                # 检查内存使用
                memory_mb = self._get_memory_usage()
                if memory_mb > self.max_memory_mb:
                    logger.warning(f"内存使用过高: {memory_mb:.1f}MB")
                    self._cleanup_resources()
                    
                # 检查临时文件数量
                with self.temp_files_lock:
                    if len(self.temp_files) > self.max_temp_files:
                        self._cleanup_temp_files()
                        
                threading.Event().wait(30)  # 30秒检查一次
                
            except Exception as e:
                logger.error(f"资源监控出错: {e}")
                
    def _cleanup_resources(self):
        """清理资源"""
        # 强制垃圾回收
        gc.collect()
        
        # 清理音频缓冲区
        self._cleanup_audio_buffers()
        
        logger.info("执行了资源清理")
        
    def _cleanup_temp_files(self):
        """清理过期临时文件"""
        with self.temp_files_lock:
            files_to_remove = []
            for file_path in self.temp_files[:]:
                try:
                    if os.path.exists(file_path):
                        # 检查文件年龄，超过1小时的删除
                        if time.time() - os.path.getctime(file_path) > 3600:
                            os.remove(file_path)
                            files_to_remove.append(file_path)
                    else:
                        files_to_remove.append(file_path)
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {file_path}, {e}")
                    
            for file_path in files_to_remove:
                self.temp_files.remove(file_path)
                
        logger.info(f"清理了 {len(files_to_remove)} 个临时文件")
        
    def register_object(self, obj):
        """注册需要跟踪的对象"""
        self.active_objects.add(obj)
        
    def get_stats(self) -> Dict:
        """获取资源使用统计"""
        with self.temp_files_lock:
            temp_files_count = len(self.temp_files)
            
        with self.audio_buffers_lock:
            audio_buffers_count = len(self.audio_buffers)
            audio_buffers_size = sum(len(data) for data in self.audio_buffers.values())
            
        return {
            'memory_mb': self._get_memory_usage(),
            'temp_files_count': temp_files_count,
            'audio_buffers_count': audio_buffers_count,
            'audio_buffers_size_mb': audio_buffers_size / 1024 / 1024,
            'active_objects_count': len(self.active_objects)
        }
        
    def shutdown(self):
        """关闭资源管理器"""
        self.monitor_running = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
            
        # 清理所有临时文件
        with self.temp_files_lock:
            for file_path in self.temp_files[:]:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except:
                    pass
            self.temp_files.clear()
            
        # 清理音频缓冲区
        with self.audio_buffers_lock:
            self.audio_buffers.clear()
            
        logger.info("资源管理器已关闭")

# 全局资源管理器实例
resource_manager = ResourceManager()