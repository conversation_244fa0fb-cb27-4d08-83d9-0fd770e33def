#!/usr/bin/env python3
"""
手动回复模式测试脚本
用于测试手动模式下的二次唤醒功能
"""

import requests
import json
import time
import sys

def test_mode_operations(host="localhost", port=8080):
    """测试模式操作"""
    base_url = f"http://{host}:{port}"
    
    print("=== 手动回复模式测试 ===\n")
    
    # 1. 获取当前状态
    print("1. 获取当前状态:")
    try:
        response = requests.get(f"{base_url}/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"   当前模式: {status.get('mode', '未知')}")
            print(f"   运行状态: {status.get('status', '未知')}")
        else:
            print(f"   ❌ 获取状态失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 获取状态失败: {e}")
    
    print()
    
    # 2. 切换到手动模式
    print("2. 切换到手动模式:")
    try:
        data = {"mode": "manual"}
        response = requests.post(f"{base_url}/mode", json=data, timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ {result.get('message', '切换成功')}")
        else:
            print(f"   ❌ 切换失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 切换失败: {e}")
        return False
    
    print()
    
    # 3. 提示用户进行唤醒测试
    print("3. 手动模式测试流程:")
    print("   📢 请按以下步骤测试:")
    print("   1) 说唤醒词（应该听到提示音，但不录音）")
    print("   2) 按回车键继续，我将发送测试回复")
    print("   3) 机器人说话后，再次说唤醒词测试二次唤醒")
    print()
    
    input("   按回车键开始测试...")
    
    # 4. 发送测试回复
    print("\n4. 发送测试回复:")
    test_text = "这是手动回复模式的测试，如果您能听到这句话，说明功能正常"
    try:
        data = {"text": test_text}
        response = requests.post(f"{base_url}/tts", json=data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 回复发送成功: {result.get('message', '成功')}")
        else:
            print(f"   ❌ 回复发送失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 回复发送失败: {e}")
    
    print()
    
    # 5. 等待并提示二次唤醒测试
    print("5. 二次唤醒测试:")
    print("   📢 现在请再次说唤醒词，测试是否能够二次唤醒")
    print("   如果能听到提示音，说明二次唤醒功能正常")
    print()
    
    input("   测试完成后按回车键继续...")
    
    # 6. 切换回自动模式
    print("\n6. 切换回自动模式:")
    try:
        data = {"mode": "auto"}
        response = requests.post(f"{base_url}/mode", json=data, timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ {result.get('message', '切换成功')}")
        else:
            print(f"   ❌ 切换失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 切换失败: {e}")
    
    return True

def main():
    """主函数"""
    # 解析命令行参数
    host = "localhost"
    port = 8080
    
    if len(sys.argv) > 1:
        port = int(sys.argv[1])
    if len(sys.argv) > 2:
        host = sys.argv[2]
    
    print(f"测试服务器: {host}:{port}\n")
    
    # 执行测试
    success = test_mode_operations(host, port)
    
    if success:
        print("\n=== 测试完成 ===")
        print("如果二次唤醒功能正常，说明手动模式工作正确")
    else:
        print("\n=== 测试失败 ===")
        print("请检查服务器是否正常运行")
    
    print(f"\n使用方法: python {sys.argv[0]} [端口] [主机]")
    print(f"示例: python {sys.argv[0]} 8080 localhost")

if __name__ == "__main__":
    main()
