"""
统一配置管理
"""
import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class APIConfig:
    """API配置"""
    access_key: str = os.getenv("JD_ACCESS_KEY", "")
    access_secret: str = os.getenv("JD_ACCESS_SECRET", "")
    bot_id: str = os.getenv("JD_BOT_ID", "")
    ws_url_base: str = "wss://joyinside.jd.com/soulmate/voiceCall/v4"

@dataclass
class AudioConfig:
    """音频配置"""
    sample_rate: int = 16000
    channels: int = 1
    chunk_size: int = 1024
    sound_threshold: int = 5000
    echo_cancel: bool = True

@dataclass
class RobotConfig:
    """机器人配置"""
    robot_type: str = "dont"  # dont, lite3, magicdog, engineai
    robot_ip: str = "*************"
    robot_port: int = 30000

class Settings:
    """全局设置"""
    def __init__(self):
        self.api = APIConfig()
        self.audio = AudioConfig()
        self.robot = RobotConfig()
        
    @classmethod
    def from_env(cls):
        """从环境变量加载配置"""
        settings = cls()
        # 从环境变量或配置文件加载
        return settings

# 全局配置实例
settings = Settings.from_env()