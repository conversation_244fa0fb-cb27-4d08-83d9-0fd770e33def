"""
API限流和重试机制
"""
import time
import threading
from typing import Dict, Optional, Callable, Any
from collections import deque, defaultdict
from functools import wraps
import random
from utils.error_handler import handle_exceptions
from util.logger import logger

class TokenBucket:
    """令牌桶算法实现"""
    
    def __init__(self, capacity: int, refill_rate: float):
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate
        self.last_refill = time.time()
        self.lock = threading.Lock()
        
    def consume(self, tokens: int = 1) -> bool:
        """消费令牌"""
        with self.lock:
            now = time.time()
            # 添加令牌
            elapsed = now - self.last_refill
            self.tokens = min(self.capacity, self.tokens + elapsed * self.refill_rate)
            self.last_refill = now
            
            # 检查是否有足够令牌
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False

class RateLimiter:
    """API限流器"""
    
    def __init__(self):
        self.buckets: Dict[str, TokenBucket] = {}
        self.request_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
    def add_limit(self, key: str, requests_per_second: float, burst_size: int = None):
        """添加限流规则"""
        if burst_size is None:
            burst_size = max(1, int(requests_per_second * 2))
            
        self.buckets[key] = TokenBucket(burst_size, requests_per_second)
        logger.info(f"添加限流规则: {key} - {requests_per_second} req/s, burst: {burst_size}")
        
    def check_limit(self, key: str, tokens: int = 1) -> bool:
        """检查是否超过限制"""
        if key not in self.buckets:
            return True  # 没有限制规则，允许通过
            
        bucket = self.buckets[key]
        allowed = bucket.consume(tokens)
        
        # 记录请求历史
        now = time.time()
        self.request_history[key].append({
            'timestamp': now,
            'allowed': allowed,
            'tokens': tokens
        })
        
        if not allowed:
            logger.warning(f"API限流触发: {key}")
            
        return allowed
        
    def get_stats(self, key: str) -> Dict[str, Any]:
        """获取限流统计"""
        if key not in self.buckets:
            return {}
            
        bucket = self.buckets[key]
        history = self.request_history[key]
        
        # 计算最近1分钟的请求统计
        now = time.time()
        recent_requests = [r for r in history if now - r['timestamp'] < 60]
        
        return {
            'available_tokens': bucket.tokens,
            'capacity': bucket.capacity,
            'refill_rate': bucket.refill_rate,
            'recent_requests_count': len(recent_requests),
            'recent_rejected_count': sum(1 for r in recent_requests if not r['allowed'])
        }

class RetryManager:
    """重试管理器"""
    
    def __init__(self):
        self.retry_stats: Dict[str, Dict] = defaultdict(lambda: {
            'total_attempts': 0,
            'success_count': 0,
            'failure_count': 0,
            'last_attempt': 0
        })
        
    def retry_with_backoff(self,
                          max_retries: int = 3,
                          base_delay: float = 1.0,
                          max_delay: float = 60.0,
                          backoff_factor: float = 2.0,
                          jitter: bool = True):
        """指数退避重试装饰器"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                func_name = f"{func.__module__}.{func.__name__}"
                stats = self.retry_stats[func_name]
                
                last_exception = None
                
                for attempt in range(max_retries + 1):
                    try:
                        stats['total_attempts'] += 1
                        stats['last_attempt'] = time.time()
                        
                        result = func(*args, **kwargs)
                        
                        stats['success_count'] += 1
                        if attempt > 0:
                            logger.info(f"{func_name} 重试成功，尝试次数: {attempt + 1}")
                        return result
                        
                    except Exception as e:
                        last_exception = e
                        stats['failure_count'] += 1
                        
                        if attempt < max_retries:
                            # 计算延迟时间
                            delay = min(base_delay * (backoff_factor ** attempt), max_delay)
                            if jitter:
                                delay *= (0.5 + random.random() * 0.5)  # 添加抖动
                                
                            logger.warning(f"{func_name} 第{attempt + 1}次尝试失败: {e}, {delay:.2f}秒后重试")
                            time.sleep(delay)
                        else:
                            logger.error(f"{func_name} 重试失败，已达最大重试次数: {max_retries}")
                            
                raise last_exception
                
            return wrapper
        return decorator
        
    def get_retry_stats(self, func_name: str = None) -> Dict:
        """获取重试统计"""
        if func_name:
            return self.retry_stats.get(func_name, {})
        return dict(self.retry_stats)

# 全局实例
rate_limiter = RateLimiter()
retry_manager = RetryManager()

# 预设常用限流规则
rate_limiter.add_limit("websocket_send", 10.0, 20)  # WebSocket发送限制
rate_limiter.add_limit("tts_request", 5.0, 10)      # TTS请求限制
rate_limiter.add_limit("asr_request", 8.0, 15)      # ASR请求限制
rate_limiter.add_limit("llm_request", 3.0, 5)       # LLM请求限制

def rate_limited(key: str, tokens: int = 1):
    """限流装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not rate_limiter.check_limit(key, tokens):
                raise Exception(f"API限流: {key}")
            return func(*args, **kwargs)
        return wrapper
    return decorator

# 常用重试装饰器
network_retry = retry_manager.retry_with_backoff(max_retries=3, base_delay=1.0)
api_retry = retry_manager.retry_with_backoff(max_retries=2, base_delay=0.5)
critical_retry = retry_manager.retry_with_backoff(max_retries=5, base_delay=2.0)