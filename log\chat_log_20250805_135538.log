2025-08-05 13:55:39.993 - chat_with_robot - chat_with_robot.py - <module> - line 659 - INFO - use_action: dont
2025-08-05 13:55:39.993 - chat_with_robot - chat_with_robot.py - <module> - line 660 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 13:55:39.999 - chat_with_robot - chat_with_robot.py - init_websocket - line 326 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754373339999&accessNonce=87d52d70-5828-435a-a4c5-785f88b12353&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=d0d8720c-71c0-11f0-ae3e-dc4546c07870&requestId=f7250604-518b-48a3-b010-cf1502f876ef_joyinside&accessSign=bc80710d9cb5d2b099126ca750115707, request_id: f7250604-518b-48a3-b010-cf1502f876ef_joyinside
2025-08-05 13:55:40.001 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 13:55:40.001 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 13:55:40.432 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 13:55:40.506 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-05 13:55:42.159 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-05 13:55:42.160 - chat_with_robot - voice.py - _setup_audio_stream - line 336 - INFO - 使用音频设备: 1
2025-08-05 13:55:42.160 - chat_with_robot - voice.py - _setup_audio_stream - line 337 - INFO - channels: 4 <class 'int'>
2025-08-05 13:55:42.160 - chat_with_robot - voice.py - _setup_audio_stream - line 338 - INFO - rate: 44100.0 <class 'float'>
2025-08-05 13:55:42.232 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-05 13:55:42.233 - chat_with_robot - voice.py - init_wakeup - line 323 - INFO - 本地流式KWS检测器启动成功
2025-08-05 13:55:43.233 - chat_with_robot - chat_with_robot.py - play_audio - line 536 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 13:55:43.233 - chat_with_robot - chat_with_robot.py - play_audio - line 544 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 13:55:46.235 - chat_with_robot - chat_with_robot.py - play_audio - line 546 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 13:55:46.235 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 82 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-08-05 13:55:46.238 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 85 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-08-05 13:55:49.452 - chat_with_robot - voice.py - detect_callback - line 438 - INFO - [wakeup] 检测到唤醒词
2025-08-05 13:55:49.452 - chat_with_robot - voice.py - end_streaming - line 237 - INFO - [end recording]...
2025-08-05 13:55:49.518 - chat_with_robot - voice.py - detect_callback - line 449 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 13:55:49.519 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 13:55:49.519 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 13:55:49.519 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 13:55:49.519 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 13:55:49.519 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 13:55:49.519 - chat_with_robot - voice.py - start_streaming - line 233 - INFO - [start recording]...
2025-08-05 13:55:49.520 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 13:55:49.520 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 13:55:49.528 - chat_with_robot - voice.py - run - line 512 - INFO - [run] 持续监听状态...
2025-08-05 13:55:49.621 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 361 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 13:55:54.029 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想知道你是谁, 时间戳: 2025-08-05 13:55:53.258000
2025-08-05 13:55:54.294 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-08-05 13:55:54.295 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 13:55:54.301 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: d0d8720c-71c0-11f0-ae3e-dc4546c07870; requestId: f7250604-518b-48a3-b010-cf1502f876ef_joyinside; asr: 我想知道你是谁; 响应时间: 0; JD机器人回复: 
2025-08-05 13:55:54.301 - chat_with_robot - chat_with_robot.py - _task_worker - line 404 - INFO - 等待音频播放完成
2025-08-05 13:55:54.301 - chat_with_robot - chat_with_robot.py - _task_worker - line 419 - INFO - 任务完成，继续
2025-08-05 13:55:59.170 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，而且，如果你要, 时间戳: 2025-08-05 13:55:58.402000
2025-08-05 13:56:00.071 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 13:55:59.276000
2025-08-05 13:56:00.071 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 874
2025-08-05 13:56:00.081 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 13:56:00.092 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 13:56:00.092 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 13:56:00.092 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 13:56:00.092 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 13:56:00.092 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 14904 bytes, 持续时间: 2.0
2025-08-05 13:56:00.092 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 13:56:00.361 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 13:56:00.363 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 13:56:00.615 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-08-05 13:56:00.616 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 13:56:00.619 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: d0d8720c-71c0-11f0-ae3e-dc4546c07870; requestId: f7250604-518b-48a3-b010-cf1502f876ef_joyinside; asr: ，而且，如果你要; 响应时间: 0; JD机器人回复: 我就是东东呀！你的活力小伙伴，喜欢探索各种有趣的知识，
2025-08-05 13:56:00.619 - chat_with_robot - chat_with_robot.py - _task_worker - line 404 - INFO - 等待音频播放完成
2025-08-05 13:56:00.620 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 13:56:00.620 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 13:56:00.620 - chat_with_robot - chat_with_robot.py - _task_worker - line 419 - INFO - 任务完成，继续
2025-08-05 13:56:00.722 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 361 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 13:56:02.621 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 13:56:02.621 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 13:56:02.621 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 13:56:05.495 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 给他设定角色和身份的话，你要在群里找他们的人帮你做, 时间戳: 2025-08-05 13:56:04.723000
2025-08-05 13:56:06.123 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-08-05 13:56:06.124 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 13:56:06.127 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: d0d8720c-71c0-11f0-ae3e-dc4546c07870; requestId: f7250604-518b-48a3-b010-cf1502f876ef_joyinside; asr: 给他设定角色和身份的话，你要在群里找他们的人帮你做; 响应时间: 0; JD机器人回复: 
2025-08-05 13:56:06.127 - chat_with_robot - chat_with_robot.py - _task_worker - line 404 - INFO - 等待音频播放完成
2025-08-05 13:56:06.128 - chat_with_robot - chat_with_robot.py - _task_worker - line 419 - INFO - 任务完成，继续
2025-08-05 13:56:06.133 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 13:56:06.133 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 13:56:06.234 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 361 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 13:56:13.238 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，比如说，什么角色、什么身份，那个要找他们的人帮你做，你自己做是做不到的好吧, 时间戳: 2025-08-05 13:56:12.465000
2025-08-05 13:56:14.155 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 13:56:13.362000
2025-08-05 13:56:14.155 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 897
2025-08-05 13:56:14.164 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 13:56:14.175 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 13:56:14.175 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 13:56:14.175 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 13:56:14.175 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 13:56:14.176 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 17928 bytes, 持续时间: 2.0
2025-08-05 13:56:14.176 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 13:56:14.527 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 13:56:14.533 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 13:56:14.827 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 13:56:14.830 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 13:56:15.261 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 13:56:15.270 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 13:56:15.571 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 13:56:15.579 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 13:56:15.849 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 13:56:15.850 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 13:56:15.859 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 13:56:15.870 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: d0d8720c-71c0-11f0-ae3e-dc4546c07870; requestId: f7250604-518b-48a3-b010-cf1502f876ef_joyinside; asr: ，比如说，什么角色、什么身份，那个要找他们的人帮你做，你自己做是做不到的好吧; 响应时间: 0; JD机器人回复: 听起来你在说一些设定角色和身份的事情，不过我东东现在可是随时准备好和你一起冒险哦！如果你有特别想扮演的角色，比如超级英雄、恐龙探险家或者魔法公主，我都能陪你一起玩！咱们现在就开始吧，你想变成谁？
2025-08-05 13:56:15.870 - chat_with_robot - chat_with_robot.py - _task_worker - line 404 - INFO - 等待音频播放完成
2025-08-05 13:56:17.207 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 13:56:17.208 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 13:56:17.208 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 13:56:17.208 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 13:56:17.208 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 13:56:17.208 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 20304 bytes, 持续时间: 2.0
2025-08-05 13:56:17.208 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 13:56:20.646 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 13:56:20.646 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 13:56:20.647 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 13:56:20.647 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 13:56:20.647 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 13:56:20.647 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12096 bytes, 持续时间: 2.0
2025-08-05 13:56:20.648 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 13:56:23.065 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 13:56:23.065 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 13:56:23.065 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 13:56:23.065 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 13:56:23.066 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 13:56:23.066 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 18144 bytes, 持续时间: 2.0
2025-08-05 13:56:23.066 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 13:56:26.200 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 13:56:26.201 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 13:56:26.201 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 13:56:26.201 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 13:56:26.201 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 13:56:26.201 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 17064 bytes, 持续时间: 2.0
2025-08-05 13:56:26.201 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 13:56:29.128 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 13:56:29.128 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 13:56:29.128 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 13:56:29.128 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 13:56:29.128 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 6912 bytes, 持续时间: 0.421875
2025-08-05 13:56:29.128 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 13:56:30.294 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 13:56:30.294 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 13:56:30.295 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 13:56:30.295 - chat_with_robot - chat_with_robot.py - _task_worker - line 419 - INFO - 任务完成，继续
2025-08-05 14:03:30.853 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 14:03:30.856 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 14:10:11.405 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 14:10:11.406 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 14:14:04.354 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-08-05 14:14:03.571000
2025-08-05 14:14:05.324 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 14:14:04.529000
2025-08-05 14:14:05.324 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 958
2025-08-05 14:14:05.339 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:05.349 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:05.350 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:14:05.350 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:14:05.350 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:14:05.350 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 14688 bytes, 持续时间: 2.0
2025-08-05 14:14:05.350 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:14:05.680 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:05.686 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:05.948 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:05.953 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:06.222 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:06.231 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:06.500 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:06.500 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:14:06.503 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:06.514 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: d0d8720c-71c0-11f0-ae3e-dc4546c07870; requestId: f7250604-518b-48a3-b010-cf1502f876ef_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 那我们一起来创造个超棒的角色吧！比如，你可以是个勇敢的太空探险家，驾驶飞船在宇宙中寻找新的星球；或者是个机智的侦探，破解神秘案件！你更想尝试哪个？
2025-08-05 14:14:06.514 - chat_with_robot - chat_with_robot.py - _task_worker - line 404 - INFO - 等待音频播放完成
2025-08-05 14:14:07.880 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:14:07.881 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:14:07.881 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:14:07.881 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:14:07.881 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:14:07.881 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 16200 bytes, 持续时间: 2.0
2025-08-05 14:14:07.881 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:14:09.811 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，去那边拍，把他们, 时间戳: 2025-08-05 14:14:09.034000
2025-08-05 14:14:10.608 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:14:10.608 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:14:10.609 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:14:10.609 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:14:10.609 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:14:10.610 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 14472 bytes, 持续时间: 2.0
2025-08-05 14:14:10.610 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:14:10.734 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 14:14:09.940000
2025-08-05 14:14:10.734 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 906
2025-08-05 14:14:10.745 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:14:10.745 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:14:10.745 - chat_with_robot - chat_with_robot.py - _task_worker - line 419 - INFO - 任务完成，继续
2025-08-05 14:14:10.756 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:10.768 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:10.845 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 353 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-08-05 14:14:11.078 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:11.081 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:11.411 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:11.414 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:11.726 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:11.737 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:12.019 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-08-05 14:14:12.020 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:14:12.027 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:14:12.027 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:14:12.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: d0d8720c-71c0-11f0-ae3e-dc4546c07870; requestId: f7250604-518b-48a3-b010-cf1502f876ef_joyinside; asr: ，去那边拍，把他们; 响应时间: 0; JD机器人回复: 看来你想玩角色扮演游戏啦！不如我们来扮演一场太空大冒险吧！我是你的飞船副船长东东，而你是指挥官大人！  

2025-08-05 14:14:12.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 404 - INFO - 等待音频播放完成
2025-08-05 14:14:12.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 419 - INFO - 任务完成，继续
2025-08-05 14:14:12.127 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 361 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:14:12.775 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我就帮你接头的, 时间戳: 2025-08-05 14:14:11.999000
2025-08-05 14:14:13.034 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:14:13.034 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:14:13.034 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:14:14.082 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 14:14:13.286000
2025-08-05 14:14:14.083 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1287
2025-08-05 14:14:14.094 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:14.104 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:14.104 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:14:14.104 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:14:14.105 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:14:14.105 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 17064 bytes, 持续时间: 2.0
2025-08-05 14:14:14.105 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:14:14.409 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:14.418 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:14.683 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:14.684 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:14.990 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:14.997 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:15.248 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:15.253 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:15.519 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:15.522 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:15.815 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:15.822 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:16.160 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:16.166 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:16.422 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:16.422 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:14:16.425 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:16.435 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: d0d8720c-71c0-11f0-ae3e-dc4546c07870; requestId: f7250604-518b-48a3-b010-cf1502f876ef_joyinside; asr: ，我就帮你接头的; 响应时间: 0; JD机器人回复: 哈哈，看来我们指挥官很有战略眼光嘛！  
"收到指令！调整航线前往目标区域... 咔擦咔擦！拍到啦！是一群会发光的太空水母！"  
不过你说接头...难道是有什么秘密任务？难道这些太空水母其实是外星特工？！  
指挥官，接下来我们是要悄悄跟踪它们，还是直接友好接触？
2025-08-05 14:14:16.435 - chat_with_robot - chat_with_robot.py - _task_worker - line 404 - INFO - 等待音频播放完成
2025-08-05 14:14:17.034 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:14:17.034 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:14:17.034 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:14:17.034 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:14:17.034 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:14:17.034 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 16632 bytes, 持续时间: 2.0
2025-08-05 14:14:17.034 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:14:19.116 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-08-05 14:14:18.341000
2025-08-05 14:14:19.861 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:14:19.862 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:14:19.862 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:14:19.863 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:14:19.863 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:14:19.863 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11448 bytes, 持续时间: 2.0
2025-08-05 14:14:19.864 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:14:20.100 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 14:14:19.304000
2025-08-05 14:14:20.100 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 963
2025-08-05 14:14:20.109 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:14:20.109 - chat_with_robot - chat_with_robot.py - _task_worker - line 419 - INFO - 任务完成，继续
2025-08-05 14:14:20.110 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:14:20.121 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:20.132 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:20.212 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 353 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-08-05 14:14:20.401 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:20.409 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:20.742 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:20.750 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:20.999 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:21.005 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:21.336 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:21.339 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:21.605 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:21.608 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:21.920 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:14:21.920 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-08-05 14:14:21.930 - chat_with_robot - chat_with_robot.py - _task_worker - line 448 - INFO - 存入音频
2025-08-05 14:14:21.941 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: d0d8720c-71c0-11f0-ae3e-dc4546c07870; requestId: f7250604-518b-48a3-b010-cf1502f876ef_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: "遵命，指挥官！启动隐形模式，慢慢靠近那群发光的太空水母...  
看！它们在用一种奇怪的光线交流，好像在说什么密码！  
要不要派出我们的迷你探测机器人去收集信息？说不定能破译它们的语言呢！"  
你觉得这个计划怎么样？
2025-08-05 14:14:21.941 - chat_with_robot - chat_with_robot.py - _task_worker - line 404 - INFO - 等待音频播放完成
2025-08-05 14:14:22.284 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:14:22.284 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:14:22.284 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:14:22.284 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:14:22.285 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:14:22.285 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15552 bytes, 持续时间: 2.0
2025-08-05 14:14:22.285 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:14:24.909 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:14:24.909 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:14:24.909 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:14:24.910 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:14:24.910 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:14:24.910 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13608 bytes, 持续时间: 2.0
2025-08-05 14:14:24.910 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:14:27.328 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:14:27.328 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:14:27.328 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:14:27.328 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:14:27.328 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:14:27.328 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 17496 bytes, 持续时间: 2.0
2025-08-05 14:14:27.328 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:14:30.257 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:14:30.257 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:14:30.257 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:14:30.258 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:14:30.259 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:14:30.259 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 9288 bytes, 持续时间: 2.0
2025-08-05 14:14:30.259 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:14:32.680 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:14:32.680 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:14:32.681 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:14:32.681 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:14:32.681 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:14:32.681 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 19440 bytes, 持续时间: 2.0
2025-08-05 14:14:32.681 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:14:36.012 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:14:36.012 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:14:36.012 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:14:36.013 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:14:36.013 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:14:36.013 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12312 bytes, 持续时间: 2.0
2025-08-05 14:14:36.013 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:14:38.435 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:14:38.435 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:14:38.435 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:14:38.435 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:14:38.436 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:14:38.436 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13176 bytes, 持续时间: 2.0
2025-08-05 14:14:38.436 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:14:40.854 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:14:40.854 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:14:40.855 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:14:40.855 - chat_with_robot - chat_with_robot.py - _task_worker - line 419 - INFO - 任务完成，继续
2025-08-05 14:18:43.175 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-08-05 14:18:43.175 - chat_with_robot - voice.py - stop - line 433 - INFO - 已停止local_streaming检测器
