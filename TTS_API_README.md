# 文字转语音 HTTP API 使用说明

## 功能介绍

本功能为现有的语音对话系统添加了HTTP API接口，支持通过网络请求直接发送文字进行语音合成，让机器人说出指定的内容。同时支持**手动回复模式**，实现唤醒后完全由人工控制回复内容。

## 两种工作模式

### 1. 自动对话模式（默认）
- 唤醒词 → 录音 → 上传音频 → ASR识别 → 大模型处理 → TTS语音回复

### 2. 手动回复模式
- 唤醒词 → **不录音，不上传** → 等待手动输入文字 → TTS语音回复

## 主要特性

- ✅ **直接文字转语音**：无需经过智能体处理，直接合成语音
- ✅ **手动回复模式**：唤醒后不录音，完全手动控制
- ✅ **模式切换**：可在自动对话和手动回复之间切换
- ✅ **隐私保护**：手动模式下不录音不上传
- ✅ **精确控制**：确保机器人说出准确的内容
- ✅ **自动打断功能**：会中断机器人当前正在说的话
- ✅ **流式音频生成**：实时生成和播放音频
- ✅ **动作同步**：支持嘴巴、脖子、眼睛动作控制
- ✅ **跨域支持**：支持网页端调用
- ✅ **简单易用**：标准HTTP接口

## 启动方式

### 1. 启动主程序（带HTTP API）

```bash
python chat_with_robot.py --http_port 8080
```

### 2. 可用参数

```bash
python chat_with_robot.py \
  --kws local_streaming \
  --use_action dont \
  --echo_cancel True \
  --http_port 8080
```

参数说明：
- `--http_port`: HTTP API服务器端口，默认8080

## API接口

### 1. 获取API信息

**请求：**
```
GET http://localhost:8080/
```

**响应：**
```json
{
  "service": "Text to Speech API",
  "endpoint": "/tts",
  "method": "POST",
  "format": {"text": "要合成的文字内容"},
  "example": "curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{\"text\":\"你好，我是机器人\"}'"
}
```

### 2. 文字转语音

**请求：**
```
POST http://localhost:8080/tts
Content-Type: application/json

{
  "text": "你好，我是机器人"
}
```

**响应（成功）：**
```json
{
  "status": "success",
  "message": "Text sent for speech synthesis"
}
```

**响应（失败）：**
```json
{
  "status": "error",
  "message": "错误描述"
}
```

## 使用示例

### 1. 命令行测试

```bash
# 基本测试
curl -X POST http://localhost:8080/tts \
  -H "Content-Type: application/json" \
  -d '{"text":"你好，我是机器人"}'

# 测试长文本
curl -X POST http://localhost:8080/tts \
  -H "Content-Type: application/json" \
  -d '{"text":"欢迎使用文字转语音功能，这个系统可以将您输入的文字直接转换为语音输出"}'
```

### 2. Python测试脚本

```bash
# 运行测试脚本
python test_tts_api.py

# 指定端口和主机
python test_tts_api.py 8080 localhost
```

### 3. Web页面测试

1. 用浏览器打开 `tts_web_test.html`
2. 确认服务器地址正确
3. 输入要合成的文字
4. 点击"发送语音合成"按钮

## 限制说明

- **文字长度**：最大1000字节（约500个中文字符）
- **编码格式**：UTF-8
- **并发限制**：建议单个请求完成后再发送下一个
- **网络要求**：需要连接到京东语音服务

## 错误处理

### 常见错误

1. **连接失败**
   - 检查程序是否正常启动
   - 确认端口号是否正确
   - 检查防火墙设置

2. **文字太长**
   - 确保文字不超过1000字节
   - 可以分段发送长文本

3. **WebSocket连接问题**
   - 检查网络连接
   - 确认京东语音服务可访问

## 集成建议

### 1. 网页集成

```javascript
async function sendTTS(text) {
    const response = await fetch('http://localhost:8080/tts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ text: text })
    });
    
    const result = await response.json();
    return result.status === 'success';
}
```

### 2. Python集成

```python
import requests

def send_tts(text, host="localhost", port=8080):
    url = f"http://{host}:{port}/tts"
    data = {"text": text}
    
    try:
        response = requests.post(url, json=data, timeout=10)
        return response.status_code == 200
    except Exception as e:
        print(f"发送失败: {e}")
        return False
```

## 应用场景

1. **远程控制**：通过手机/电脑远程让机器人说话
2. **预设回复**：快速播放常用语句
3. **紧急通知**：重要消息的即时播报
4. **多媒体展示**：配合展示系统的语音解说
5. **自动化集成**：与其他系统联动的语音提示

## 注意事项

1. 确保主程序正常运行后再使用API
2. 文字转语音会打断当前正在播放的语音
3. 建议在局域网内使用，避免网络延迟
4. 长时间运行建议监控程序状态
