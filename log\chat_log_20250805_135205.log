2025-08-05 13:52:08.195 - chat_with_robot - chat_with_robot.py - <module> - line 659 - INFO - use_action: dont
2025-08-05 13:52:08.196 - chat_with_robot - chat_with_robot.py - <module> - line 660 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 13:52:08.202 - chat_with_robot - chat_with_robot.py - init_websocket - line 326 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754373128203&accessNonce=cbc38111-784c-4e62-ab49-506dce615ae5&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=529ab730-71c0-11f0-95d5-dc4546c07870&requestId=64cf1ee7-e782-4299-ab2d-3abb37cc9406_joyinside&accessSign=e974e11f8f67171306d87d8c022bd546, request_id: 64cf1ee7-e782-4299-ab2d-3abb37cc9406_joyinside
2025-08-05 13:52:08.202 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 13:52:08.202 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 13:52:08.735 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 13:52:08.842 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-05 13:52:10.554 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-05 13:52:10.556 - chat_with_robot - voice.py - _setup_audio_stream - line 336 - INFO - 使用音频设备: 0
2025-08-05 13:52:10.556 - chat_with_robot - voice.py - _setup_audio_stream - line 337 - INFO - channels: 2 <class 'int'>
2025-08-05 13:52:10.556 - chat_with_robot - voice.py - _setup_audio_stream - line 338 - INFO - rate: 44100.0 <class 'float'>
2025-08-05 13:52:10.615 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-05 13:52:10.615 - chat_with_robot - voice.py - init_wakeup - line 323 - INFO - 本地流式KWS检测器启动成功
2025-08-05 13:52:11.616 - chat_with_robot - chat_with_robot.py - play_audio - line 536 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 13:52:11.617 - chat_with_robot - chat_with_robot.py - play_audio - line 544 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 13:52:14.619 - chat_with_robot - chat_with_robot.py - play_audio - line 546 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 13:52:14.619 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 82 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-08-05 13:52:14.621 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 85 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-08-05 13:54:04.889 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-08-05 13:54:04.889 - chat_with_robot - voice.py - stop - line 433 - INFO - 已停止local_streaming检测器
