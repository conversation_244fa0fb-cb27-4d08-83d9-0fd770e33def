FROM python:3.8-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    portaudio19-dev \
    libasound-dev \
    libportaudio2 \
    libportaudiocpp0 \
    ffmpeg \
    libpulse-dev \
    build-essential \
    sox \
    libsox-fmt-all \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .
COPY requirements-dev.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app
ENV LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/app/aec/lib

# 暴露端口（如果需要）
EXPOSE 8000

# 启动命令
CMD ["python", "chat_with_robot.py", "--use_action", "dont", "--kws", "local_streaming", "--echo_cancel", "True"]