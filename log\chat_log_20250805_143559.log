2025-08-05 14:36:00.315 - chat_with_robot - chat_with_robot.py - <module> - line 921 - INFO - use_action: dont
2025-08-05 14:36:00.315 - chat_with_robot - chat_with_robot.py - <module> - line 922 - INFO - 
[启动HardwareAIAgent交互程序]

2025-08-05 14:36:00.322 - chat_with_robot - chat_with_robot.py - init_websocket - line 533 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1754375760322&accessNonce=7d7d2ad0-bd37-45e8-bad2-54a86ddc16a1&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=73787217-71c6-11f0-9154-dc4546c07870&requestId=3c1eab3d-7147-4bdf-905a-816fe92525b2_joyinside&accessSign=75b444e9d43d43f77bd0d38e432142b9, request_id: 3c1eab3d-7147-4bdf-905a-816fe92525b2_joyinside
2025-08-05 14:36:00.324 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-08-05 14:36:00.324 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-08-05 14:36:00.852 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-08-05 14:36:00.942 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-08-05 14:36:02.631 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-08-05 14:36:02.631 - chat_with_robot - voice.py - _setup_audio_stream - line 339 - INFO - 使用音频设备: 1
2025-08-05 14:36:02.631 - chat_with_robot - voice.py - _setup_audio_stream - line 340 - INFO - channels: 4 <class 'int'>
2025-08-05 14:36:02.631 - chat_with_robot - voice.py - _setup_audio_stream - line 341 - INFO - rate: 44100.0 <class 'float'>
2025-08-05 14:36:02.685 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-08-05 14:36:02.686 - chat_with_robot - voice.py - init_wakeup - line 326 - INFO - 本地流式KWS检测器启动成功
2025-08-05 14:36:03.686 - chat_with_robot - chat_with_robot.py - play_audio - line 796 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-08-05 14:36:03.687 - chat_with_robot - chat_with_robot.py - play_audio - line 804 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-08-05 14:36:06.690 - chat_with_robot - chat_with_robot.py - play_audio - line 806 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-08-05 14:36:06.690 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 261 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-08-05 14:36:06.691 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 264 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-08-05 14:36:06.696 - chat_with_robot - chat_with_robot.py - start_http_server - line 488 - INFO - HTTP API服务器已启动，监听端口: 8080
2025-08-05 14:36:06.696 - chat_with_robot - chat_with_robot.py - start_http_server - line 489 - INFO - API地址: http://localhost:8080/tts
2025-08-05 14:36:06.696 - chat_with_robot - chat_with_robot.py - start_http_server - line 490 - INFO - 使用示例: curl -X POST http://localhost:8080/tts -H 'Content-Type: application/json' -d '{"text":"你好，我是机器人"}'
2025-08-05 14:36:06.727 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:36:06.727 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 14:36:06.791 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:36:06.791 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:36:06.791 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:36:06.792 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:36:06.792 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:36:06.792 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 14:36:06.792 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 14:36:06.795 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:36:06.795 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:36:06.797 - chat_with_robot - voice.py - run - line 524 - INFO - [run] 持续监听状态...
2025-08-05 14:36:06.897 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:36:06.995 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS / HTTP/1.1" 200 -
2025-08-05 14:36:07.311 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "GET / HTTP/1.1" 200 -
2025-08-05 14:36:07.569 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /pending HTTP/1.1" 200 -
2025-08-05 14:36:07.624 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /pending HTTP/1.1" 200 -
2025-08-05 14:36:08.744 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 14:36:08.744 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 14:36:08.871 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /mode HTTP/1.1" 200 -
2025-08-05 14:36:09.191 - chat_with_robot - chat_with_robot.py - toggle_manual_reply_mode - line 568 - INFO - 已切换到: 手动回复模式
2025-08-05 14:36:09.191 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /mode HTTP/1.1" 200 -
2025-08-05 14:36:09.353 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-08-05 14:36:09.353 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-08-05 14:36:12.857 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:36:12.857 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 14:36:12.919 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:36:12.920 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:36:12.920 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:36:12.920 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:36:12.921 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:36:12.921 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 14:36:12.921 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 14:36:12.922 - chat_with_robot - voice.py - detect_callback - line 492 - INFO - 🔧 手动回复模式 - 等待手动输入回复内容
2025-08-05 14:36:12.930 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:36:12.930 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:36:13.031 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:36:17.880 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 14:36:18.201 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 561 - INFO - 发送文字转语音: 今天天气真不错，适合出去走走
2025-08-05 14:36:18.201 - chat_with_robot - chat_with_robot.py - do_POST - line 112 - INFO - 🔧 手动回复完成，重置为监听状态，等待下次唤醒
2025-08-05 14:36:18.201 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 14:36:18.557 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-08-05 14:36:17.745000
2025-08-05 14:36:18.558 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1754375777745
2025-08-05 14:36:18.588 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:36:18.593 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:36:18.593 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:36:18.593 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:36:18.593 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:36:18.593 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 8856 bytes, 持续时间: 2.0
2025-08-05 14:36:18.593 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:36:18.888 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:36:18.896 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:36:21.013 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:36:21.013 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:36:21.013 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:36:21.013 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:36:21.014 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 7560 bytes, 持续时间: 0.46142578125
2025-08-05 14:36:21.014 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:36:22.283 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:36:22.283 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:36:22.283 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:36:24.808 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:36:24.808 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 14:36:24.870 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:36:24.870 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:36:24.870 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:36:24.871 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:36:24.871 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:36:24.871 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 14:36:24.872 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 14:36:24.872 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 14:36:24.872 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 14:36:24.873 - chat_with_robot - voice.py - detect_callback - line 492 - INFO - 🔧 手动回复模式 - 等待手动输入回复内容
2025-08-05 14:36:24.973 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:36:29.804 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 14:36:30.116 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 561 - INFO - 发送文字转语音: 你好，我是机器人
2025-08-05 14:36:30.118 - chat_with_robot - chat_with_robot.py - do_POST - line 112 - INFO - 🔧 手动回复完成，重置为监听状态，等待下次唤醒
2025-08-05 14:36:30.118 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 14:36:30.407 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:36:30.418 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:36:30.418 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:36:30.418 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:36:30.418 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 4320 bytes, 持续时间: 0.263671875
2025-08-05 14:36:30.418 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:36:30.656 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:36:30.666 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:36:31.183 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:36:31.183 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:36:31.183 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:36:31.184 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:36:31.184 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 6480 bytes, 持续时间: 0.3955078125
2025-08-05 14:36:31.184 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:36:32.301 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:36:32.302 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:36:32.302 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:36:37.958 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:36:37.959 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 14:36:38.023 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:36:38.023 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:36:38.023 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:36:38.023 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:36:38.023 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-08-05 14:36:38.023 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-08-05 14:36:38.024 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 14:36:38.024 - chat_with_robot - voice.py - detect_callback - line 492 - INFO - 🔧 手动回复模式 - 等待手动输入回复内容
2025-08-05 14:36:38.026 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:36:38.026 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:36:38.127 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:36:39.244 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 14:36:39.563 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 561 - INFO - 发送文字转语音: 系统正在处理中，请稍等
2025-08-05 14:36:39.563 - chat_with_robot - chat_with_robot.py - do_POST - line 112 - INFO - 🔧 手动回复完成，重置为监听状态，等待下次唤醒
2025-08-05 14:36:39.563 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 14:36:39.921 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:36:39.930 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:36:39.931 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:36:39.931 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:36:39.931 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-08-05 14:36:39.931 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 8640 bytes, 持续时间: 2.0
2025-08-05 14:36:39.932 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:36:40.214 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:36:40.218 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:36:42.229 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:36:42.229 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 14:36:42.294 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:36:42.294 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:36:42.294 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:36:42.294 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 14:36:42.294 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:36:42.296 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:36:42.296 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:36:42.296 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:36:42.296 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 14:36:42.296 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:36:42.297 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 14:36:42.297 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 14:36:42.297 - chat_with_robot - voice.py - detect_callback - line 492 - INFO - 🔧 手动回复模式 - 等待手动输入回复内容
2025-08-05 14:36:42.397 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:36:45.921 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /tts HTTP/1.1" 200 -
2025-08-05 14:36:46.238 - chat_with_robot - chat_with_robot.py - send_text_to_speech - line 561 - INFO - 发送文字转语音: 收到您的问题，让我来回答
2025-08-05 14:36:46.238 - chat_with_robot - chat_with_robot.py - do_POST - line 112 - INFO - 🔧 手动回复完成，重置为监听状态，等待下次唤醒
2025-08-05 14:36:46.238 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "POST /tts HTTP/1.1" 200 -
2025-08-05 14:36:46.535 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:36:46.537 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:36:46.537 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:36:46.537 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:36:46.538 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 7776 bytes, 持续时间: 0.474609375
2025-08-05 14:36:46.538 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:36:46.788 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-08-05 14:36:46.795 - chat_with_robot - chat_with_robot.py - _task_worker - line 708 - INFO - 存入音频
2025-08-05 14:36:47.858 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:36:47.858 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:36:47.858 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-08-05 14:36:47.858 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-08-05 14:36:47.859 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 6480 bytes, 持续时间: 0.3955078125
2025-08-05 14:36:47.859 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-08-05 14:36:48.978 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-08-05 14:36:48.978 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-08-05 14:36:48.978 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-08-05 14:38:57.444 - chat_with_robot - voice.py - detect_callback - line 441 - INFO - [wakeup] 检测到唤醒词
2025-08-05 14:38:57.444 - chat_with_robot - voice.py - end_streaming - line 240 - INFO - [end recording]...
2025-08-05 14:38:57.509 - chat_with_robot - voice.py - detect_callback - line 452 - INFO - [wakeup] 清空WebSocket接收队列
2025-08-05 14:38:57.509 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:38:57.509 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:38:57.509 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-08-05 14:38:57.510 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-08-05 14:38:57.510 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-08-05 14:38:57.510 - chat_with_robot - voice.py - start_streaming - line 236 - INFO - [start recording]...
2025-08-05 14:38:57.510 - chat_with_robot - voice.py - detect_callback - line 492 - INFO - 🔧 手动回复模式 - 等待手动输入回复内容
2025-08-05 14:38:57.513 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-08-05 14:38:57.513 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-08-05 14:38:57.614 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 604 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-08-05 14:39:00.683 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS / HTTP/1.1" 200 -
2025-08-05 14:39:00.947 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "GET / HTTP/1.1" 200 -
2025-08-05 14:39:00.996 - chat_with_robot - chat_with_robot.py - log_message - line 229 - INFO - HTTP "OPTIONS /pending HTTP/1.1" 200 -
