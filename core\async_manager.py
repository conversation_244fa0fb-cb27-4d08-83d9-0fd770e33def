"""
异步任务管理器 - 优化并发处理性能
"""
import asyncio
import threading
from typing import Callable, Any, Optional, Dict
from concurrent.futures import ThreadPoolExecutor, Future
import queue
import time
from utils.error_handler import handle_exceptions
from util.logger import logger

class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        
        # 线程池执行器
        self.executor = ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="async_task"
        )
        
        # 任务队列
        self.high_priority_queue = queue.PriorityQueue()
        self.normal_priority_queue = queue.Queue()
        
        # 任务跟踪
        self.active_tasks: Dict[str, Future] = {}
        self.task_results: Dict[str, Any] = {}
        
        # 工作线程
        self.worker_threads = []
        self.running = True
        
        # 启动工作线程
        self._start_workers()
        
    def _start_workers(self):
        """启动工作线程"""
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"async_worker_{i}",
                daemon=True
            )
            worker.start()
            self.worker_threads.append(worker)
            
    def _worker_loop(self):
        """工作线程主循环"""
        while self.running:
            try:
                # 优先处理高优先级任务
                try:
                    priority, task_id, func, args, kwargs = self.high_priority_queue.get(timeout=0.1)
                    self._execute_task(task_id, func, args, kwargs)
                    continue
                except queue.Empty:
                    pass
                    
                # 处理普通任务
                try:
                    task_id, func, args, kwargs = self.normal_priority_queue.get(timeout=0.5)
                    self._execute_task(task_id, func, args, kwargs)
                except queue.Empty:
                    continue
                    
            except Exception as e:
                logger.error(f"工作线程异常: {e}")
                
    @handle_exceptions()
    def _execute_task(self, task_id: str, func: Callable, args: tuple, kwargs: dict):
        """执行任务"""
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            self.task_results[task_id] = {
                'result': result,
                'success': True,
                'duration': time.time() - start_time
            }
            logger.debug(f"任务 {task_id} 执行成功，耗时 {time.time() - start_time:.3f}s")
        except Exception as e:
            self.task_results[task_id] = {
                'error': str(e),
                'success': False,
                'duration': time.time() - start_time
            }
            logger.error(f"任务 {task_id} 执行失败: {e}")
        finally:
            # 清理活跃任务记录
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
                
    def submit_task(self, 
                   func: Callable, 
                   *args, 
                   task_id: Optional[str] = None,
                   priority: int = 0,
                   **kwargs) -> str:
        """提交任务"""
        if task_id is None:
            task_id = f"task_{int(time.time() * 1000000)}"
            
        # 检查任务是否已存在
        if task_id in self.active_tasks:
            logger.warning(f"任务 {task_id} 已存在，跳过")
            return task_id
            
        # 添加到相应队列
        if priority > 0:
            self.high_priority_queue.put((priority, task_id, func, args, kwargs))
        else:
            self.normal_priority_queue.put((task_id, func, args, kwargs))
            
        # 记录活跃任务
        future = Future()
        self.active_tasks[task_id] = future
        
        logger.debug(f"任务 {task_id} 已提交，优先级: {priority}")
        return task_id
        
    def get_task_result(self, task_id: str, timeout: float = None) -> Optional[Any]:
        """获取任务结果"""
        start_time = time.time()
        while True:
            if task_id in self.task_results:
                result_info = self.task_results.pop(task_id)
                if result_info['success']:
                    return result_info['result']
                else:
                    raise Exception(result_info['error'])
                    
            if timeout and (time.time() - start_time) > timeout:
                raise TimeoutError(f"任务 {task_id} 超时")
                
            time.sleep(0.01)
            
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.active_tasks:
            future = self.active_tasks[task_id]
            if future.cancel():
                del self.active_tasks[task_id]
                logger.info(f"任务 {task_id} 已取消")
                return True
        return False
        
    def get_queue_stats(self) -> Dict[str, int]:
        """获取队列统计信息"""
        return {
            'high_priority_queue_size': self.high_priority_queue.qsize(),
            'normal_priority_queue_size': self.normal_priority_queue.qsize(),
            'active_tasks_count': len(self.active_tasks),
            'pending_results_count': len(self.task_results)
        }
        
    def shutdown(self, wait: bool = True):
        """关闭任务管理器"""
        self.running = False
        
        # 取消所有活跃任务
        for task_id, future in self.active_tasks.items():
            future.cancel()
            
        # 关闭线程池
        self.executor.shutdown(wait=wait)
        
        # 等待工作线程结束
        if wait:
            for worker in self.worker_threads:
                worker.join(timeout=5)
                
        logger.info("异步任务管理器已关闭")

# 全局异步任务管理器
async_manager = AsyncTaskManager()