"""
统一错误处理
"""
import functools
import traceback
from typing import Callable, Any
from util.logger import logger

class RobotError(Exception):
    """机器人相关错误基类"""
    pass

class AudioError(RobotError):
    """音频处理错误"""
    pass

class NetworkError(RobotError):
    """网络连接错误"""
    pass

class ConfigError(RobotError):
    """配置错误"""
    pass

def handle_exceptions(error_type: type = Exception, default_return: Any = None):
    """异常处理装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except error_type as e:
                logger.error(f"{func.__name__} 执行失败: {str(e)}")
                logger.debug(f"详细错误信息: {traceback.format_exc()}")
                return default_return
            except Exception as e:
                logger.error(f"{func.__name__} 未预期错误: {str(e)}")
                logger.debug(f"详细错误信息: {traceback.format_exc()}")
                return default_return
        return wrapper
    return decorator

def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise
                    logger.warning(f"{func.__name__} 第{attempt + 1}次尝试失败: {str(e)}")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator