"""
@Author: <EMAIL> <EMAIL> <EMAIL>
@Create Date: 2025.04.23
@Description: 

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
import requests
import json
import time
from api.ai_agent_related.is_valid_check_list import META_ACTIONS
# from ai_agent_related.is_valid_check_list import META_ACTIONS
import re
from functools import lru_cache

TIMEOUT = 2  # seconds - 减少超时时间提升响应速度

# 本地快速匹配规则 - 常见指令直接匹配，无需网络请求
QUICK_MATCH_RULES = {
    r'(前进|向前|往前)': ["前进"],
    r'(后退|向后|往后)': ["后退"],
    r'(左转|向左|往左)': ["左转"],
    r'(右转|向右|往右)': ["右转"],
    r'(停|停下|停止)': ["停止运动"],
    r'(跳|跳跃)': ["向前跳"],
    r'(舞|跳舞|舞蹈)': ["舞蹈"],
    r'(招呼|你好|打招呼)': ["打招呼"],
    r'(撒娇|可爱)': ["摇尾巴撒娇"],
    r'(兴奋|开心|高兴)': ["兴奋"],
    r'(沮丧|难过|伤心)': ["沮丧"],
}

EXAMPLES = [
    [
        "你今天心情怎么样",
        ["打招呼"]
    ],
    [
        "你会扭秧歌吗",
        ["左转", "右转", "扭身体"]
    ],
    [
        "你能给我跳支舞吗",
        ["舞蹈"]
    ],
    [
        "咚咚好可爱啊",
        ["摇尾巴撒娇"]
    ],
    [
        "咚咚跑两步",
        ["前进", "前进"]
    ],
    [
        "向左转个圈",
        ["左转", "左转", "左转", "左转", "左转", "左转", "左转", "左转", "左转", "左转"]
    ],
    [
        "咚咚撒个娇",
        ["摇尾巴撒娇"]
    ],
    [
        "咚咚求抱抱",
        ["向前跳"]
    ],
    [
        "快停下",
        ["停止运动"]
    ],
    [
        "向右转90度",
        ["右转", "右转", "右转"]
    ],
    [
        "走路狂野点",
        ["关闭遇到障碍停止模式"]
    ],
    [
        "走路小心点",
        ["开启遇到障碍停止模式"]
    ],
    [
        "咚咚你真好看",
        ["兴奋", "摇尾巴撒娇"]
    ],
    [
        "快点啦",
        ["抬头"]
    ],
    [
        "你都会做什么呀",
        ["悠闲"]
    ],
    [
        "好喜欢你",
        ["兴奋"]
    ],
    [
        "我来讲个故事",
        ["倾听"]
    ],
    [
        "讨厌你",
        ["沮丧"]
    ]
    ,
    [
        "你能跑两步吗",
        ["高速", "前进", "前进"]
    ],
    [
        "你能快走两步吗",
        ["中速", "前进", "前进"]
    ],
    [
        "别走那么快",
        ["抬头", "低速"]
    ],
    [
        "跟着我",
        ["开启跟随模式"]
    ],
    [
        "不用跟着我啦",
        ["关闭跟随模式"]
    ],
    [
        "来个厉害的招式",
        ["扭身跳"]
    ],
    [
        "跟着我跑",
        ["开启跟随模式", "中速"]
    ],
    [
        "跟着我冲",
        ["开启跟随模式", "高速"]
    ],
    [
        "慢着点",
        ["低速"]
    ],
    [
        "快点",
        ["中速"]
    ],
        [
        "跑起来",
        ["高速"]
    ],
    [
        "跟我溜达溜达",
        ["开启跟随模式", "低速"]
    ],
    [
        "跟我走吧",
        ["开启跟随模式", "低速"]
    ],
]

def chat_with_gpt_w_ai_agent_magicdog(content, current_state, examples=EXAMPLES, meta_actions=META_ACTIONS, robot_name="咚咚"):
    data = {
        "content": content,
        "current_state": current_state,
        "examples": examples,
        "meta_actions": meta_actions,
        "robot_name": robot_name
    }
    timeout = TIMEOUT
    try:
        response = requests.post("http://116.198.74.181:80/arrange_action", json=data, timeout=timeout)  # v3
        response.raise_for_status()  # Raise an error for bad responses
        data = response.json()
        json_str = json.dumps(data, ensure_ascii=False)
    except requests.exceptions.Timeout:
        print(f"Request timed out after {timeout} seconds")
        data = {
            "actions": [
                "请求超时"
            ],
            "is_valid": False
        }
        json_str = json.dumps(data, ensure_ascii=False)
    return json_str

if __name__ == '__main__':
    start_time = time.time()
    content = "跑两步"
    current_state = "力控状态（静止站立）且步态为平地低速步态"
    json_str = chat_with_gpt_w_ai_agent_magicdog(content, current_state)
    print(json_str)
    print(f"Time taken: {time.time() - start_time:.2f} seconds")